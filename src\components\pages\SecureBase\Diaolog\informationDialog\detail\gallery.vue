<template>
    <div>
        <div class="bottom-all-wrapper">
            
            <div class="bottom">
            <div class="table-list">
                    <div class="table-item" v-for="(item,index) in tableData" :key="index">
                        <el-image
                            class="table-img"
                            :src="item.actucallImg"
                            :preview-src-list="[item.viewPath]"
                        >
                        </el-image>
                        <div class="table-text">
                            <div>{{item.fileName}}({{item.photosCount}})</div>
                            <div class="table-text-bottom">
                                <div>{{item.updatedDate}}</div>
                                <img class="table-text-icon" src="@/assets/newImages/Dialog/right-arrow-icon.svg" @click="openDetail(item)"/>
                            </div>
                        </div>
                    </div>
            </div>
                <el-pagination
                    class="pagination"
                    background
                    v-model:currentPage="pageNum"
                    :page-size="pageSize"
                    @current-change="handleCurrentChange"
                    layout="->,total, prev, pager, next"
                    :total="total"
                />
            </div>

        </div>
        <!-- <photo v-if="detailShow" :sonId="sonId" :pid="pid" @closeDialog="closeDetail"></photo> -->

    </div>
</template>

<script setup>
import {
    page_photo_album,
} from "@/assets/js/api/dialog/secureBase";

import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    computed,
    defineEmits,
    getCurrentInstance,
    toRefs
} from "vue";
import photo from './photo.vue'
const { proxy } = getCurrentInstance();
const emit = defineEmits(["openPhotoDialog"]);

const props = defineProps({
    sonId: {
        type: [String, Number],
    },
});
const { sonId } = toRefs(props);
let queryParams = ref({});
const allData = ref([]);
const tableData = ref([]);
const pageNum = ref(1);
const pageSize = ref(8);
const total = ref(0);
//相册id
let pid = ref(null);


const handleCurrentChange = (val) => {
    pageNum.value = val;
    tableData.value = allData.value.slice((pageNum.value - 1) * pageSize.value, pageNum.value * pageSize.value);
    console.log(tableData.value,'llsssssaaaaaaaaa');
    
};

const getData = () => {
    page_photo_album({
        pageNum: pageNum.value,
        pageSize: pageSize.value,
        enterpriseId:sonId.value,
        ...queryParams.value
    }).then((res) => {
        console.log(res);
        if (res.data && res.data.data) {
            allData.value=res.data.data.list;
            total.value = res.data.data.total;
            console.log(allData.value,'llsssssaaaaaaaaa');
            // allData.value = [
            //     {
            //         name:1
            //     },
            //     {
            //         name:1
            //     },
            //     {
            //         name:1
            //     },
            //     {
            //         name:1
            //     },
            //     {
            //         name:1
            //     },
            //     {
            //         name:1
            //     },
            //     {
            //         name:1
            //     },
            //     {
            //         name:1
            //     },
            //     {
            //         name:1
            //     },
            //     {
            //         name:1
            //     },
            // ];
            // total.value = 11;
            handleCurrentChange(1)
            // tableData.value = res.data.data.list.map((i) => {
            //     return {
            //         ...i,
            //         awardFileObj: i.awardFile ? JSON.parse(i.awardFile) : "",
            //     };
            // });
        }
    });
};
//打开弹窗
const openDetail = (item) =>{
    console.log(item,'llsssssaaaaaaaaa');
    pid.value = item.id;
    proxy.$emit('openPhotoDialog',pid.value);

}
onMounted(() => {
    console.log(sonId.value,'llsssssaaaaaaaaa');
    
    getData();
});
onBeforeUnmount(() => {});
</script>

<style lang="less" scoped>
.bottom-all-wrapper{
    width: 1120px;
    height: 600px;
display: flex;
        flex-direction: column;
        gap:20px;

}
.top-content {
            display: flex;
            justify-content: space-between;

            .select-option {
                width: 1120px;
                height: 40px;
                display: flex;
                gap: 20px;
                align-items: center;
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                letter-spacing: 0%;
                text-align: right;
                color: rgba(128, 234, 255, 1);

                .option-item {
                    display: flex;
                    gap: 12px;
                    align-items: center;
                }
                .title {
                    // padding-right: 12px;
                    line-height: 40px; /* 157.143% */
                }
                .el-input {
                    width: 210px;
                    height: 40px;
                }
                .el-select {
                    width: 210px;
                }
                .el-date-editor {
                    width: 210px;
                }
                :deep(.el-input__wrapper) {
                    height: 20px;
                    padding: 10px 12px;
                }
               
            }
            .select-btns {
                width: 192px;
                height: 40px;
                display: flex;
                gap: 16px;
                color: #fff;
                text-align: center;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 40px; /* 142.857% */
                .search-btn {
                    width: 88px;
                    height: 40px;
                    border-radius: 4px;
                    border: 0.5px solid #30abe8;
                    background: rgba(48, 171, 232, 0.3);
                }
                .reset-btn {
                    width: 88px;
                    height: 40px;
                    border-radius: 4px;
                    border: 0.5px solid #ffc61a;
                    background: rgba(255, 198, 26, 0.3);
                }
            }
            .select2 {
                margin-top: 16px;
            }
        }
.bottom {
    width: 1120px;
    height: 504px;
    .table-list{
        width: 1120px;
    max-height: 420px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        .table-item{
            width: 250.88px;
            height: 186px;
            padding: 8px 7.56px;
            border-radius: 4px;
            border-width: 1px;
            background-color: #1A9FFF1A;
            border: 1px solid #1A9FFF99;
            .table-img{
                width: 250.88px;
                height: 143px;
                
            }
            .table-text{
                     width: 250.88px;
            height: 36px;
                    // margin-top: 8px;
                    font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: #80EAFF;
.table-text-bottom{
    display: flex;
    justify-content: space-between;
    align-items: center;
    table-text-icon{
        width: 16px;
height: 16px;


    }
}
                }
        }

    }
    .tablebox {
        //表格四个边框的颜色
        // border: 1px solid #30abe8 !important;
        border: none !important;
        border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;

        th.el-table__cell {
            border: none !important;
        }
    }

    :deep(.el-table),
    :deep(.el-table__expanded-cell) {
        background-color: transparent !important;
        color: #ffffff;
    }

    // 设置表头颜色
    :deep(.el-table th) {
        // border-bottom: 1px solid #30abe8 !important;
        height: 40px;
        background-color: rgba(25, 159, 255, 0.1) !important;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: 0%;
        text-align: center;
        color: rgba(128, 234, 255, 1);
    }

    .el-table__header-wrapper,
    .el-table__fixed-header-wrapper {
        //表头的样式
        th {
            border-bottom: none !important;
        }
    }

    /* 表格内背景颜色 */

    :deep(.el-table) tr,
    :deep(.el-table) td {
        background-color: transparent !important;
    }

    :deep(.el-table) tr td {
        background-color: transparent !important;
        height: 58px;
        color: #fff;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
        letter-spacing: -0.28px;
        // border-color: #30ABE8 !important;
    }
    // 表头样式（去掉底部边框）
    :deep(.el-table__header) {
        .el-table__cell {
            border-bottom: none !important;
            background-color: rgba(25, 159, 255, 0.1) !important;
            color: rgba(128, 234, 255, 1);
        }
    }

    // 表格内容样式（保留底部边框）
    :deep(.el-table__body) {
        .el-table__cell {
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;
            height: 40px;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color: rgba(255, 255, 255, 1);
        }
    }

    /* 去掉table 最底部的线 */
    :deep(.el-table__inner-wrapper::before) {
        left: 0;
        bottom: 0;
        width: 100%;
        height: 0;
        z-index: 3;
    }

    .pagination {
        margin-top: 16px;
    }

    :deep(.el-pagination .el-pager li:not(.is-active)) {
        background-color: transparent;
        border: 1px solid rgba(26, 159, 255, 1);
        /*进行修改未选中背景和字体*/
        color: rgba(26, 159, 255, 1);
        text-align: center;
        border-radius: 1px;
        border: 1px solid #30abe8;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
    }

    :deep(.el-pagination .el-pager li:not(.is-disabled).is-active) {
        border-radius: 1px;
        border: 1px solid rgba(255, 198, 26, 1);
        background: rgba(255, 198, 26, 0.15);

        color: #47ebeb;
        text-align: center;

        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: rgba(255, 255, 255, 1);
    }

    :deep(.el-pagination .el-pager li:hover) {
        border-radius: 1px;
        border: 1px solid rgba(255, 198, 26, 1);
        background: rgba(255, 198, 26, 0.15);

        color: #47ebeb;
        text-align: center;

        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: rgba(255, 255, 255, 1);
    }

    :deep(.el-pagination .btn-next) {
        background-color: transparent;
        border: 1px solid rgba(26, 159, 255, 1);
        /*进行修改未选中背景和字体*/
        color: rgba(26, 159, 255, 1);
        text-align: center;
        border-radius: 1px;
        border: 1px solid #30abe8;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
    }

    :deep(.el-pagination .btn-prev) {
        background-color: transparent;
        border: 1px solid rgba(26, 159, 255, 1);
        /*进行修改未选中背景和字体*/
        color: rgba(26, 159, 255, 1);
        text-align: center;
        border-radius: 1px;
        border: 1px solid #30abe8;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
    }

    :deep(.el-pagination__total) {
        background-color: transparent;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        text-align: right;
        vertical-align: middle;
        color: rgba(26, 159, 255, 1);
    }
}
</style>
