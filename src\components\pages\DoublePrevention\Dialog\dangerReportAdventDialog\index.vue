<template>
    <div class="dialog-all-wrapper">
        <div class="risk-control-check-wrapper">
            <div class="risk-control-check border">
                <div class="dialog-header">
                    <div class="header-title">临期/超期隐患</div>
                    <img
                        src="@/assets/newImages/Dialog/dialog-close.svg"
                        class="header-close"
                        @click="closeDialog"
                    />
                </div>
                <div class="dialog-content">
                    <div class="top-content">
                        <div class="select-option">
                            <div class="option-item">
                                <div class="title">企业名称</div>
                                <el-input
                                    v-model="queryParams.companyName"
                                    placeholder="请输入"
                                    clearable
                                />
                            </div>
                            <div class="option-item">
                                <div class="title">隐患名称</div>
                                <el-input
                                    v-model="queryParams.dangerName"
                                    placeholder="请输入"
                                    clearable
                                />
                            </div>
                            <div class="option-item">
                                <div class="title">隐患来源</div>
                                <el-select
                                    v-model="queryParams.riskSource"
                                    class="m-2"
                                    placeholder="请选择"
                                    @change="change"
                                    filterable
                                    clearable
                                >
                                    <el-option
                                        v-for="item in dangerSourceOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </div>
                             <div class="option-item">
                                <div class="title">隐患类型</div>
                                <el-select
                                    v-model="queryParams.riskType"
                                    class="m-2"
                                    placeholder="请选择"
                                    @change="change"
                                    filterable
                                    clearable
                                >
                                    <el-option
                                        v-for="item in riskTypeOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </div>
                             <div class="option-item">
                                <div class="title">隐患等级</div>
                                <el-select
                                    v-model="queryParams.levelStatue"
                                    class="m-2"
                                    placeholder="请选择"
                                    @change="change"
                                    filterable
                                    clearable
                                >
                                    <el-option
                                        v-for="item in dangerLevelOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </div>
                             <div class="option-item">
                                <div class="title">治理类型</div>
                                <el-select
                                    v-model="queryParams.reformType"
                                    class="m-2"
                                    placeholder="请选择"
                                    @change="change"
                                    filterable
                                    clearable
                                >
                                    <el-option
                                        v-for="item in reformTypeOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </div>
                            
                            
                            
                           
                              
                           
                            <div class="option-item">
                                <div class="title">时间范围筛选</div>
                                <el-select
                                    v-model="chooseTimeBtn"
                                    class="m-2"
                                    placeholder="请选择"
                                    @change="selectBtn"
                                    filterable
                                    clearable
                                >
                                    <el-option
                                        v-for="item in btnOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </div>
                            
                            <div class="option-item">
                                <div class="title">隐患状态</div>
                                <el-checkbox-group v-model="queryParams.reforms" @change="handleCheckboxChange" class="custom-checkbox-group">
                                    <el-checkbox
                                        v-for="item in dangerReformOptions"
                                        :key="item.value"
                                        :label="item.value"
                                        :class="{'yellow-checkbox': queryParams.taskTypes && queryParams.taskTypes.includes(item.value)}"
                                    >
                                        {{ item.label }}
                                    </el-checkbox>
                                </el-checkbox-group>
                            </div>
                            
                            <div class="option-item">
                                <div class="title">期限状态</div>
                                <el-select
                                    v-model="queryParams.deadlineStatus"
                                    class="m-2"
                                    placeholder="请选择"
                                    @change="change"
                                    filterable
                                    clearable
                                >
                                    <el-option
                                        v-for="item in deadlineStatusOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </div>
                             <div class="option-item">
                                <div class="title">隐患类别</div>
                                <el-checkbox-group v-model="queryParams.riskCategories" @change="handleCheckboxChange" class="custom-checkbox-group">
                                    <el-checkbox
                                        v-for="item in riskCategoryOptions"
                                        :key="item.value"
                                        :label="item.value"
                                        :class="{'yellow-checkbox': queryParams.taskTypes && queryParams.taskTypes.includes(item.value)}"
                                    >
                                        {{ item.label }}
                                    </el-checkbox>
                                </el-checkbox-group>
                            </div>
                            
                        </div>

                        <div class="select-btns">
                            <div class="search-btn" @click="getData">
                                搜索
                            </div>
                            <div class="reset-btn" @click="resetSearch">
                                重置
                            </div>
                        </div>
                    </div>
                    <div class="bottom">
                    <el-table
                        class="tablebox"
                        :data="tableData"
                        style="width: 100%"
                    >
                        <el-table-column
                            prop="companyName"
                            label="企业名称"
                            width="220"
                            show-overflow-tooltip
                        />
                        <el-table-column
                            prop="dangerName"
                            label="隐患名称"
                            show-overflow-tooltip
                            width="160"
                        />
                        <el-table-column
                            prop="reportPeopleName"
                            label="排查人员"
                            show-overflow-tooltip
                            width="120"
                        />
                        <el-table-column
                            prop="riskFind"
                            label="排查时间"
                            show-overflow-tooltip
                            width="160"
                        >
                        <!-- <template #default="scope">
                            {{ dictList&&dictList.find(i=>i.dictEncoding==scope.row.nature)!=undefined?dictList.find(i=>i.dictEncoding==scope.row.nature).dictName:'-' }}
                        </template> -->
                        </el-table-column>
                        <el-table-column
                            prop="levelStatueValue"
                            label="隐患等级"
                            show-overflow-tooltip
                            width="120"
                        />
                        <el-table-column
                            prop="riskSourceValue"
                            label="隐患来源"
                            show-overflow-tooltip
                            width="180"
                        />
                        <el-table-column
                            prop="riskTypeValue"
                            label="隐患类型"
                            show-overflow-tooltip
                            width="120"
                        />
                        <el-table-column
                            prop="riskCategoryValue"
                            label="隐患类别"
                            show-overflow-tooltip
                            width="140"
                        />
                        <el-table-column
                            prop="reformTypeValue"
                            label="治理类型"
                            show-overflow-tooltip
                            width="120"
                        />
                        <el-table-column
                            prop="chargePeopleName"
                            label="整改责任人"
                            show-overflow-tooltip
                            width="120"
                        />
                        <el-table-column
                            prop="deadline"
                            label="隐患治理期限"
                            show-overflow-tooltip
                            width="160"
                        />
                        <el-table-column
                            prop="reformValue"
                            label="隐患状态"
                            show-overflow-tooltip
                            width="120"
                        />
                    </el-table>
                    <el-pagination
                        class="pagination"
                        background
                        v-model:currentPage="pageNum"
                        :page-size="pageSize"
                        @current-change="handleCurrentChange"
                        layout="->,total, prev, pager, next"
                        :total="total"
                    />
                </div>
                </div>
            </div>
            <div class="corner-bottom-left"></div>
        </div>
    </div>
</template>

<script setup>
import {
    ref,
    reactive, 
    onMounted,
    onBeforeUnmount,
    computed,
    defineEmits,
    toRef,
    isRef,
    toRefs,
    watch,
    nextTick,
} from "vue";
import { danger_report,get_department_tree,task_type_enum,task_state_enum } from "@/assets/js/api/dialog/doublePrevention";
import { danger_source_enum,risk_type_enum,reform_type_enum,danger_level_enum,risk_category_enum,danger_reform_enum } from "@/assets/js/api/dialog/doublePrevention";

const emit = defineEmits(["closeDialog"]);
const props = defineProps({
    currentValue: {
        type: [String,Number],
    },
});

const { currentValue } = toRefs(props);
//部门树
let departOptions = ref([]);
//选中部门
let selectDepart = ref([]);
//任务类型
let taskTypeOptions = ref([]);
//任务状态
let taskStateOptions = ref([]);
//隐患来源
let dangerSourceOptions = ref([]);
//隐患类型
let riskTypeOptions = ref([]);
//隐患等级
let dangerLevelOptions = ref([]);
//治理类型
let reformTypeOptions = ref([]);
//隐患类别
let riskCategoryOptions = ref([]);
//隐患状态
let dangerReformOptions = ref([]);
//期限状态
let deadlineStatusOptions = ref([
    {
        value:1,
        label:'临期'
    },
    {
        value:2,
        label:'超期'
    }
])
//按钮选项
const btnOptions = ref([
    {
        label:'今日',
        value:1
    },
    {
        label:'本周',
        value:2
    },
    {
        label:'本月',
        value:3
    },
    {
        label:'本年',
        value:4
    }
]);
//选择时间
let chooseTimeBtn = ref(null);
    //获取日期时间

// 获取本周第一天（周一）
const getFirstDayOfWeek = (date) => {
    const day = date.getDay() || 7;
    const diff = date.getDate() - day + 1;
    return new Date(date.setDate(diff));
};

// 定义时间变量
const currentDate = ref(new Date());

// 格式化日期时间函数
const formatDateTime = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 今天23:59:59
const todayEnd = computed(() => {
    const date = new Date(currentDate.value);
    date.setHours(23, 59, 59, 999);
    return formatDateTime(date);
});

// 今天00:00:00
const todayStart = computed(() => {
    const date = new Date(currentDate.value);
    date.setHours(0, 0, 0, 0);
    return formatDateTime(date);
});

// 本周第一天00:00:00
const weekStart = computed(() => {
    const date = getFirstDayOfWeek(new Date(currentDate.value));
    date.setHours(0, 0, 0, 0);
    return formatDateTime(date);
});

// 本月第一天00:00:00
const monthStart = computed(() => {
    const date = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth(), 1);
    date.setHours(0, 0, 0, 0);
    return formatDateTime(date);
});

// 本年第一天00:00:00
const yearStart = computed(() => {
    const date = new Date(currentDate.value.getFullYear(), 0, 1);
    date.setHours(0, 0, 0, 0);
    return formatDateTime(date);
});
const selectBtn = (val) => {
    chooseTimeBtn.value=val;
    console.log(val,'vallllllllll测试弹窗');
    if(val==1){
        beginTime.value=todayStart.value;
        endTime.value=todayEnd.value;
    }else if(val==2){
        beginTime.value=weekStart.value;
        endTime.value=todayEnd.value;
    }else if(val==3){
        beginTime.value=monthStart.value;
        endTime.value=todayEnd.value;
    }else if(val==4){
        beginTime.value=yearStart.value;
        endTime.value=todayEnd.value;
    }
    console.log(beginTime.value,'beginTime');
    
};
let beginTime = ref('');
let endTime = ref('');
//查询条件
let queryParams = ref({});
const tableData = ref([]);
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);
let value2 = ref(null);


const handleCurrentChange = (val) => {
    pageNum.value = val;
    getData()
};
//获取部门树
const getDepart = () =>{
    get_department_tree().then((res)=>{
        if(res.data&&res.data.data){
            departOptions.value = res.data.data;
        }
    })
}

//获取隐患来源枚举项
const getDangerSource = () =>{
    danger_source_enum().then((res)=>{
        if(res.data&&res.data.data){
            dangerSourceOptions.value = Object.entries(res.data.data).map(([value, label]) => ({
            value,
            label
            }));
        }
    })
}
//获取隐患类型枚举项
const getRiskType = () =>{
    risk_type_enum().then((res)=>{
        if(res.data&&res.data.data){
            riskTypeOptions.value = Object.entries(res.data.data).map(([value, label]) => ({
            value,
            label
            }));
        }
    })
}
//获取隐患等级枚举项
const getDangerLevel = () =>{
    danger_level_enum().then((res)=>{
        if(res.data&&res.data.data){
            dangerLevelOptions.value = Object.entries(res.data.data).map(([value, label]) => ({
            value,
            label
            }));
        }
    })
}
//获取治理类型枚举项
const getReformType = () =>{
    reform_type_enum().then((res)=>{
        if(res.data&&res.data.data){
            reformTypeOptions.value = Object.entries(res.data.data).map(([value, label]) => ({
            value,
            label
            }));
        }
    })
}
//获取隐患类别枚举项
const getRiskCategory = () =>{
    risk_category_enum().then((res)=>{
        if(res.data&&res.data.data){
            riskCategoryOptions.value = Object.entries(res.data.data).map(([value, label]) => ({
            value,
            label
            }));
        }
    })
}
//获取隐患状态枚举项
const getDangerReform = () =>{
    danger_reform_enum().then((res)=>{
        if(res.data&&res.data.data){
            dangerReformOptions.value = Object.entries(res.data.data).map(([value, label]) => ({
            value,
            label
            }));
        }
    })
}
const getData = () =>{
     // 截取数组最后一位并设置到queryParams对象里，属性名为depart
    if (selectDepart.value && selectDepart.value.length > 0) {
        queryParams.value.depart = selectDepart.value[selectDepart.value.length - 1];
    }
    danger_report({
        pageNum:pageNum.value,
        pageSize:pageSize.value,
        beginTime:beginTime.value,
        endTime:endTime.value,
        queryType:1,
        ...queryParams.value
    }).then((res)=>{
        console.log(res);
        if(res.data&&res.data.data){
            tableData.value=res.data.data.list;
            total.value=res.data.data.total
        }
    })
}
//重置搜索
const resetSearch = () => {
    queryParams.value = {};
    selectDepart.value =[];
    chooseTimeBtn.value = null;
    beginTime.value = '';
    endTime.value = '';
    pageNum.value = 1;
    getData();
}
let detailShow = ref(false);
//详情对象
let sonObj = ref(null)
//打开详情弹窗
const toDetile = (val) =>{
    sonObj.value = val;
    detailShow.value = true
} 
//关闭详情弹窗
const closeDetail = (val) => {
    detailShow.value = false;
}
getDangerSource();
getRiskType();
getDangerLevel();
getReformType();
getRiskCategory();
getDangerReform();
onMounted(() => {
    getDepart();
    getData()
});
onMounted(() => {});
const closeDialog = () => {
    emit("closeDialog", "dangerReportAdventDialogShow");
};
watch(
    currentValue,
    (a, b) => {
        console.log(a, b,'监听lalallalalal');

        if(a&&a!=b){
             selectBtn(a);
            nextTick(()=>{
                getData()
            })
            
        }
        
    },
    {
        immediate: true,
    },
);
</script>

<style lang="less" scoped>
.dialog-all-wrapper {
    width: 100vw;
    height: 100vh;
    z-index: 1003;
    // background-color:antiquewhite;
    background-color: rgba(0, 0, 0, 0.5);

    position: absolute;
    top: 0px;
    left: 0px;
    .risk-control-check-wrapper {
        width: 1160px;
        height: 888px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        // 添加底部两个装饰三角形
        &::after,
        .corner-bottom-left {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 6px;
            border-color: rgba(26, 159, 255, 0.6) transparent transparent
                transparent;
            z-index: 1;
        }

        // 左下角装饰三角形
        .corner-bottom-left {
            bottom: -7.5px;
            left: -6px;
            transform: rotate(45deg);
        }

        // 右下角装饰三角形
        &::after {
            bottom: -7.5px;
            right: -7.5px;
            transform: rotate(-45deg);
        }
    }
    .risk-control-check {
        width: 1160px;
        height: 888px;

        background-color: rgba(5, 29, 46, 0.6);

        clip-path: polygon(
            10px 0,
            /* 左上角开始 */ calc(100% - 10px) 0,
            /* 右上角左侧 */ 100% 10px,
            /* 右上角下侧 */ 100% calc(100% - 10px),
            /* 右下角上侧 */ calc(100% - 10px) 100%,
            /* 右下角左侧 */ 10px 100%,
            /* 左下角右侧 */ 0 calc(100% - 10px),
            /* 左下角上侧 */ 0 10px /* 左上角下侧 */
        );
    }
    .dialog-header {
        width: 1128px;
        height: 56px;
        padding: 0 16px;
        margin-left: -1px;
        background: url("@/assets/newImages/Dialog/dialog-header.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-title {
            font-family: MStiffHei PRC;
            font-weight: 400;
            font-size: 18px;
            line-height: 26px;
            letter-spacing: 0%;
            background: linear-gradient(
                    180deg,
                    rgba(26, 159, 255, 0.45) 20%,
                    rgba(26, 159, 255, 0) 80%
                ),
                linear-gradient(180deg, #ffffff 15.63%, #2ad9ff 87.5%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .header-close {
            width: 16px;
            height: 16px;
        }
    }
    .border {
        border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
        /* 使用渐变作为边框图像 */
        border-image-source: linear-gradient(
            360deg,
            rgba(26, 159, 255, 0.6) 0%,
            rgba(26, 159, 255, 0) 100%
        );
        border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
        border-image-width: 1px;
        border-top: none;
    }
    // 弹窗内部
    .dialog-content {
        width: 1120px;
        height: 792px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap:20px;
        .top-content {
            display: flex;
            justify-content: space-between;
            .select-option {
                width: 1120px;
                height: 264px;
                display: flex;
                gap: 20px;
                flex-wrap: wrap;
                align-items: center;
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                letter-spacing: 0%;
                text-align: right;
                color: rgba(128, 234, 255, 1);

                .option-item {
                    display: flex;
                    gap: 12px;
                    align-items: center;
                }
                .title {
                    // padding-right: 12px;
                    line-height: 40px; /* 157.143% */
                }
                .el-input {
                    width: 210px;
                    height: 40px;
                }
                .el-select {
                    width: 210px;
                    height: 40px;

                }
                :deep(.el-select__wrapper){
                    height: 40px !important;
                    padding: 10px 12px !important;
                }
                .el-date-editor {
                    width: 210px;
                }
              :deep(.el-input__wrapper) {
                    height: 20px;
                    padding: 10px 12px;
                }
                /* 复选框容器样式 */
                .checkbox-container {
                    display: flex;
                    align-items: center;
                }

                .custom-checkbox-group {
                    display: flex;
                    flex-wrap: wrap;
                }

                /* 黄色复选框样式 - 选中状态 */
                :deep(.yellow-checkbox .el-checkbox__input.is-checked .el-checkbox__inner) {
                    background-color: rgba(255, 198, 26, 0.3) !important;
                    border-color: rgba(255, 198, 26, 1) !important;
                }

                :deep(.yellow-checkbox .el-checkbox__input.is-checked .el-checkbox__inner::after) {
                    border-color: rgba(255, 255, 255, 1) !important;
                }
                
            }
            .select-btns {
                width: 192px;
                height: 40px;
                display: flex;
                gap: 16px;
                color: #fff;
                text-align: center;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 40px; /* 142.857% */
                .search-btn {
                    width: 88px;
                    height: 40px;
                    border-radius: 4px;
                    border: 0.5px solid #30abe8;
                    background: rgba(48, 171, 232, 0.3);
                }
                .reset-btn {
                    width: 88px;
                    height: 40px;
                    border-radius: 4px;
                    border: 0.5px solid #ffc61a;
                    background: rgba(255, 198, 26, 0.3);
                }
            }
            .select2 {
                margin-top: 16px;
            }
        }
        .bottom {
        width: 1120px;
        height: 560px;
        margin-top: 16px;
        .tablebox {
            //表格四个边框的颜色
            // border: 1px solid #30abe8 !important;
            border:none !important;
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;

            th.el-table__cell {
                border: none !important;
            }
        }

        :deep(.el-table),
        :deep(.el-table__expanded-cell) {
            background-color: transparent !important;
            color: #ffffff;
        }

        // 设置表头颜色
        :deep(.el-table th) {
            // border-bottom: 1px solid #30abe8 !important;
            height: 40px;
            background-color: rgba(25, 159, 255, 0.1) !important;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color: rgba(128, 234, 255, 1);
        }

        .el-table__header-wrapper,
        .el-table__fixed-header-wrapper {
            //表头的样式
            th {
                border-bottom: none !important;
            }
        }

        /* 表格内背景颜色 */

        :deep(.el-table) tr,
        :deep(.el-table) td {
            background-color: transparent !important;
        }

        :deep(.el-table) tr td {
            background-color: transparent !important;
            height: 58px;
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            /* 157.143% */
            letter-spacing: -0.28px;
            // border-color: #30ABE8 !important;
        }
          // 表头样式（去掉底部边框）
        :deep(.el-table__header) {
            .el-table__cell {
            border-bottom: none !important; 
            background-color: rgba(25, 159, 255, 0.1) !important;
            color: rgba(128, 234, 255, 1);
            }
        }

        // 表格内容样式（保留底部边框）
        :deep(.el-table__body) {
            .el-table__cell {
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;
            height: 40px;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color:rgba(255, 255, 255, 1)
            }
        }

        /* 去掉table 最底部的线 */
        :deep(.el-table__inner-wrapper::before) {
            left: 0;
            bottom: 0;
            width: 100%;
            height: 0;
            z-index: 3;
        }

        .pagination {
            margin-top: 16px;
        }

        :deep(.el-pagination .el-pager li:not(.is-active)) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;

        }

        :deep(.el-pagination .el-pager li:not(.is-disabled).is-active) {
            border-radius: 1px;
            border: 1px solid rgba(255, 198, 26, 1);
            background: rgba(255, 198, 26, 0.15);

            color: #47ebeb;
            text-align: center;

            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: rgba(255, 255, 255, 1);

        }

        :deep(.el-pagination .el-pager li:hover) {
            border-radius: 1px;
            border: 1px solid rgba(255, 198, 26, 1);
            background: rgba(255, 198, 26, 0.15);

            color: #47ebeb;
            text-align: center;

            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            color: rgba(255, 255, 255, 1);
        }

        :deep(.el-pagination .btn-next) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }

        :deep(.el-pagination .btn-prev) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }

        :deep(.el-pagination__total) {
            background-color: transparent;
            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
text-align: right;
vertical-align: middle;
color: rgba(26, 159, 255, 1);
        }
    }
    .operateButton{
        color:rgba(128, 234, 255, 1)
    }
    }
}
</style>
