<svg width="400" height="256" viewBox="0 0 400 256" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_212_10)">
<rect x="24.6429" y="24.6429" width="70.7143" height="70.7143" rx="35.3571" stroke="url(#paint0_linear_212_10)" stroke-width="1.28571"/>
<circle cx="60" cy="60" r="27.3571" fill="url(#paint1_linear_212_10)" stroke="url(#paint2_linear_212_10)" stroke-width="1.28571"/>
<g clip-path="url(#clip1_212_10)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M49.3334 53.1814C49.3334 52.0698 50.0229 51.0748 51.0637 50.6845L58.1274 48.0356C59.3348 47.5829 60.6653 47.5829 61.8727 48.0356L68.9364 50.6845C69.9772 51.0748 70.6667 52.0698 70.6667 53.1814V61.3334C70.6667 65.253 65.5457 70.4528 60.3217 71.9148C60.1109 71.9738 59.8892 71.9738 59.6784 71.9148C54.4544 70.4528 49.3334 65.253 49.3334 61.3334V53.1814ZM62 54L56 61H59L58 66L64 59H61L62 54Z" fill="white"/>
</g>
<path d="M124 94L138.684 74H385.316L400 94H124Z" fill="url(#paint3_linear_212_10)" fill-opacity="0.6"/>
<path d="M385.121 74.3855L399.239 93.6145H124.761L138.879 74.3855H385.121Z" stroke="url(#paint4_linear_212_10)" stroke-opacity="0.8" stroke-width="0.770972"/>
<rect x="24.6429" y="160.643" width="70.7143" height="70.7143" rx="35.3571" stroke="url(#paint5_linear_212_10)" stroke-width="1.28571"/>
<circle cx="60" cy="196" r="27.3571" fill="url(#paint6_linear_212_10)" stroke="url(#paint7_linear_212_10)" stroke-width="1.28571"/>
<g clip-path="url(#clip2_212_10)">
<path d="M60 190.947C64.1855 190.947 67.5789 194.341 67.5789 198.526V203.579H52.4211V198.526C52.4211 194.341 55.8145 190.947 60 190.947V190.947ZM60 184C60.335 184 60.6563 184.133 60.8932 184.37C61.1301 184.607 61.2632 184.928 61.2632 185.263V187.789C61.2632 188.124 61.1301 188.446 60.8932 188.683C60.6563 188.92 60.335 189.053 60 189.053C59.665 189.053 59.3437 188.92 59.1068 188.683C58.8699 188.446 58.7368 188.124 58.7368 187.789V185.263C58.7368 184.928 58.8699 184.607 59.1068 184.37C59.3437 184.133 59.665 184 60 184V184ZM70.2714 188.255C70.5082 188.492 70.6412 188.813 70.6412 189.148C70.6412 189.483 70.5082 189.804 70.2714 190.041L68.4853 191.827C68.3687 191.948 68.2294 192.044 68.0752 192.11C67.9211 192.176 67.7554 192.211 67.5877 192.213C67.4199 192.214 67.2536 192.182 67.0984 192.119C66.9431 192.055 66.8021 191.961 66.6835 191.843C66.5649 191.724 66.4711 191.583 66.4076 191.428C66.3441 191.273 66.3121 191.106 66.3136 190.939C66.315 190.771 66.3499 190.605 66.4161 190.451C66.4823 190.297 66.5785 190.158 66.6992 190.041L68.4853 188.255C68.7221 188.018 69.0434 187.885 69.3783 187.885C69.7133 187.885 70.0345 188.018 70.2714 188.255V188.255ZM49.7286 188.255C49.9655 188.018 50.2867 187.885 50.6217 187.885C50.9566 187.885 51.2779 188.018 51.5147 188.255L53.3008 190.041C53.4215 190.158 53.5177 190.297 53.5839 190.451C53.6501 190.605 53.685 190.771 53.6864 190.939C53.6879 191.106 53.6559 191.273 53.5924 191.428C53.5289 191.583 53.4351 191.724 53.3165 191.843C53.1979 191.961 53.0569 192.055 52.9016 192.119C52.7464 192.182 52.5801 192.214 52.4123 192.213C52.2446 192.211 52.0789 192.176 51.9248 192.11C51.7706 192.044 51.6313 191.948 51.5147 191.827L49.7286 190.041C49.4918 189.804 49.3588 189.483 49.3588 189.148C49.3588 188.813 49.4918 188.492 49.7286 188.255V188.255ZM49.2632 205.474H70.7368C71.0719 205.474 71.3931 205.607 71.63 205.844C71.8669 206.081 72 206.402 72 206.737C72 207.072 71.8669 207.393 71.63 207.63C71.3931 207.867 71.0719 208 70.7368 208H49.2632C48.9281 208 48.6069 207.867 48.37 207.63C48.1331 207.393 48 207.072 48 206.737C48 206.402 48.1331 206.081 48.37 205.844C48.6069 205.607 48.9281 205.474 49.2632 205.474V205.474Z" fill="white"/>
</g>
<path d="M124 230L138.684 210H385.316L400 230H124Z" fill="url(#paint8_linear_212_10)" fill-opacity="0.6"/>
<path d="M385.121 210.385L399.239 229.615H124.761L138.879 210.385H385.121Z" stroke="url(#paint9_linear_212_10)" stroke-opacity="0.8" stroke-width="0.770972"/>
</g>
<defs>
<linearGradient id="paint0_linear_212_10" x1="60" y1="24" x2="60" y2="96" gradientUnits="userSpaceOnUse">
<stop stop-color="#1AC6FF"/>
<stop offset="1" stop-color="#1AC6FF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_212_10" x1="60" y1="88" x2="60" y2="32" gradientUnits="userSpaceOnUse">
<stop stop-color="#1AC6FF" stop-opacity="0"/>
<stop offset="1" stop-color="#1AC6FF" stop-opacity="0.3"/>
</linearGradient>
<linearGradient id="paint2_linear_212_10" x1="88" y1="60" x2="32" y2="60" gradientUnits="userSpaceOnUse">
<stop stop-color="#9CECFC"/>
<stop offset="0.5" stop-color="#1AC6FF" stop-opacity="0.45"/>
<stop offset="1" stop-color="#9CECFC"/>
</linearGradient>
<linearGradient id="paint3_linear_212_10" x1="194.668" y1="74" x2="194.668" y2="94" gradientUnits="userSpaceOnUse">
<stop stop-color="#30ABE8" stop-opacity="0"/>
<stop offset="1" stop-color="#30ABE8"/>
</linearGradient>
<linearGradient id="paint4_linear_212_10" x1="194.668" y1="74" x2="194.668" y2="94" gradientUnits="userSpaceOnUse">
<stop stop-color="#30ABE8" stop-opacity="0"/>
<stop offset="1" stop-color="#30ABE8"/>
</linearGradient>
<linearGradient id="paint5_linear_212_10" x1="60" y1="160" x2="60" y2="232" gradientUnits="userSpaceOnUse">
<stop stop-color="#1AC6FF"/>
<stop offset="1" stop-color="#1AC6FF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint6_linear_212_10" x1="60" y1="224" x2="60" y2="168" gradientUnits="userSpaceOnUse">
<stop stop-color="#1AC6FF" stop-opacity="0"/>
<stop offset="1" stop-color="#1AC6FF" stop-opacity="0.3"/>
</linearGradient>
<linearGradient id="paint7_linear_212_10" x1="88" y1="196" x2="32" y2="196" gradientUnits="userSpaceOnUse">
<stop stop-color="#9CECFC"/>
<stop offset="0.5" stop-color="#1AC6FF" stop-opacity="0.45"/>
<stop offset="1" stop-color="#9CECFC"/>
</linearGradient>
<linearGradient id="paint8_linear_212_10" x1="194.668" y1="210" x2="194.668" y2="230" gradientUnits="userSpaceOnUse">
<stop stop-color="#30ABE8" stop-opacity="0"/>
<stop offset="1" stop-color="#30ABE8"/>
</linearGradient>
<linearGradient id="paint9_linear_212_10" x1="194.668" y1="210" x2="194.668" y2="230" gradientUnits="userSpaceOnUse">
<stop stop-color="#30ABE8" stop-opacity="0"/>
<stop offset="1" stop-color="#30ABE8"/>
</linearGradient>
<clipPath id="clip0_212_10">
<rect width="400" height="256" fill="white"/>
</clipPath>
<clipPath id="clip1_212_10">
<rect width="32" height="32" fill="white" transform="translate(44 44)"/>
</clipPath>
<clipPath id="clip2_212_10">
<rect width="32" height="32" fill="white" transform="matrix(-1 0 0 1 76 180)"/>
</clipPath>
</defs>
</svg>
