<template>
    <div :class="`atmospheric-monitoring-dialog${size}`">
        <div class="header">
            <div class="title">大气监测</div>
            <div class="close" @click="closeDialog"></div>
        </div>
        <div class="container">
            <div class="btns">
                <div
                    :class="typeActive === 1 ? 'type-active-btn' : 'type-btn'"
                    @click="selectType(1)"
                >
                    实时监测
                </div>
                <div
                    :class="typeActive === 2 ? 'type-active-btn' : 'type-btn'"
                    @click="selectType(2)"
                >
                    统计分析
                </div>
            </div>
            <div class="separate"></div>
            <div class="monitor" v-show="typeActive === 1">
                <div class="today">
                    <img
                        class="img"
                        src="../../../../assets/images/card/weather.svg"
                        alt=""
                    />
                    <div class="top-liner-one"></div>
                    <div class="first">
                        <div class="big-text">{{ weather.temperature }}℃</div>
                        <div class="text">{{ weather.weather }}</div>
                    </div>
                    <div class="top-liner"></div>
                    <div class="second">
                        <div class="big-text">{{ nowWeather.aqi }}</div>
                        <div class="text">AQI</div>
                    </div>
                    <div class="top-liner"></div>
                    <div class="third">
                        <div class="text-right">
                            {{ weather.windDirection }}{{ weather.windPower }}级
                        </div>
                        <div class="text-right">
                            湿度{{ weather.humidity }}%
                        </div>
                    </div>
                    <div class="top-liner"></div>
                    <div class="forth">
                        <div class="text-right">空气质量</div>
                        <div class="text-right">{{ nowWeather.category }}</div>
                    </div>
                </div>
                <div class="interval"></div>
                <div class="prediction">
                    <div
                        class="prediction-wrapper"
                        v-for="(item, index) in predictionList"
                        :key="index"
                    >
                        <div class="show">
                            <div class="date">{{ item.factor_name }}</div>
                            <!-- <img class="img" src="../../../../assets/images/card/weather.svg" alt=""> -->
                            <div class="temperature">
                                {{ item.monitor_value }}
                            </div>
                        </div>
                        <div
                            class="bottom-liner"
                            v-if="index < predictionList.length - 1"
                        ></div>
                    </div>
                </div>
            </div>
            <div class="analysis" v-show="typeActive === 2">
                <div class="linewrap" ref="linewrap"></div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    defineEmits,
    nextTick,
    toRef,
    isRef,
    computed,
    toRefs,
} from "vue";
import * as echarts from "echarts";
import { setSize } from "../../../../assets/js/echartsSetSize";
import {
    eair_quality,
    now,
} from "../../../../assets/js/api/environmentalManagement";
const weather = ref({
    humidity: "",
    temperature: "",
    weather: "",
    windDirection: "",
    windPower: "",
});
const nowWeather = ref({
    aqi: "",
    category: "",
});
const size = ref(1);
const props = defineProps({
    pickId: {
        type: [Number, String],
    },
});
const clickId = ref();
const { pickId } = toRefs(props);
const predictionList = ref([
    {
        factor_name: "CO",
        monitor_value: "",
    },
    {
        factor_name: "PM2.5",
        monitor_value: "",
    },
    {
        factor_name: "PM10",
        monitor_value: "",
    },
    {
        factor_name: "O3",
        monitor_value: "",
    },
    {
        factor_name: "SO2",
        monitor_value: "",
    },
    {
        factor_name: "NO2",
        monitor_value: "",
    },
]);
watch(
    pickId,
    (a, b) => {
        console.log(a, b);
        clickId.value = a;
    },
    {
        immediate: true,
    },
);

const emit = defineEmits(["closeDialog"]);
const typeActive = ref(1);
const selectType = (type) => {
    typeActive.value = type;
    size.value = type;
    if (type == 2) {
        lineChart = echarts.init(linewrap.value);
        initChart();
        lineChart.setOption(option);
        window.addEventListener("resize", () => {
            lineChart.resize();
        });
    }
};

let option;
let linewrap = ref(null);
let lineChart;
const initChart = () => {
    option = {
        xAxis: {
            type: "category",
            data: [
                "01:00",
                "02:00",
                "03:00",
                "04:00",
                "05:00",
                "06:00",
                "07:00",
            ],
            axisLine: {
                //  改变x轴颜色
                lineStyle: {
                    color: "#1AB2FF",
                },
            },
            axisLabel: {
                //  改变x轴字体颜色和大小
                textStyle: {
                    color: "#fff",
                    fontSize: 14,
                    lineHeight: 22,
                },
            },
            axisTick: {
                show: false,
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ["rgba(255, 255, 255, 0.15)"],
                    width: 1,
                    type: "solid",
                },
            },
        },
        yAxis: {
            type: "value",
            axisLine: {
                //  改变y轴颜色
                show: false,
                lineStyle: {
                    color: "#26D9FF",
                },
            },
            axisLabel: {
                //  改变y轴字体颜色和大小
                //formatter: '{value} m³ ', //  给y轴添加单位
                textStyle: {
                    color: "#fff",
                    fontSize: 14,
                    lineHeight: 22,
                },
            },
            axisTick: {
                show: false,
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ["#315070"],
                    width: 1,
                    type: "solid",
                },
            },
        },
        grid: {
            left: 10,
            right: 10,
            top: 40,
            bottom: 10,
            containLabel: true,
        },
        series: [
            {
                smooth: true,
                symbol: "circle",
                symbolSize: 7,
                markPoint: {
                    symbol: "circle",
                },
                markLine: {
                    symbol: "none",
                    label: {
                        normal: {
                            show: false,
                            color: "#fff",
                            backgroundColor: "rgba(228,0,54,70)",
                            fontSize: 16,
                            padding: 4,
                            borderRadius: 4,
                            show: true,
                            position: "start",
                            distance: 4,
                        },
                    },
                    lineStyle: {
                        type: "dotted",
                        color: "#FF4C4D",
                        width: 1,
                    },
                    data: [
                        {
                            yAxis: 400,
                        },
                        { yAxis: 220 },
                    ],
                },
                data: [220, 332, 601, 234, 490, 730, 590],
                type: "line",
                itemStyle: {
                    normal: {
                        label: {
                            show: true,
                            color: "#fff",
                            fontSize: 12,
                        },
                        color: "#294E8F",
                        borderColor: "3D7EEB",
                        borderWidth: 2,
                    },
                },
                lineStyle: {
                    normal: {
                        width: 2,
                        color: "#1AB2FF",
                        // shadowColor: "#3D7EEB",
                        // shadowBlur: 10
                    },
                },
                areaStyle: {
                    color: {
                        type: "linear",
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0.5,
                                color: "rgba(26, 178, 255, 0.30)", // 0% 处的颜色
                            },
                            {
                                offset: 1,
                                color: "rgba(26, 178, 255, 0.00)", // 100% 处的颜色
                            },
                        ],
                        global: false, // 缺省为 false
                    },
                },
            },
        ],
    };
};
const closeDialog = () => {
    console.log("atmospheric");
    emit("closeDialog", "atmospheric");
};
const searchEairQuality = () => {
    predictionList.value = [
        {
            factor_name: "CO",
            monitor_value: "",
        },
        {
            factor_name: "PM2.5",
            monitor_value: "",
        },
        {
            factor_name: "PM10",
            monitor_value: "",
        },
        {
            factor_name: "O3",
            monitor_value: "",
        },
        {
            factor_name: "SO2",
            monitor_value: "",
        },
        {
            factor_name: "NO2",
            monitor_value: "",
        },
    ];
    eair_quality({ id: clickId.value }).then((re) => {
        if (re.data.success && re.data.data && re.data.data.length) {
            //   predictionList.value = re.data.data;
            predictionList.value.forEach((item, index) => {
                let find = re.data.data.find(
                    (x) => x.field_name == item.factor_name,
                );
                if (find) {
                    Reflect.set(item, "monitor_value", find.monitor_value);
                }
            });
        }
    });
};
const getLocation = () => {
    AMap.plugin("AMap.CitySearch", function () {
        var citySearch = new AMap.CitySearch();
        citySearch.getLocalCity(function (status, result) {
            if (status === "complete" && result.info === "OK") {
                // address.value = result.city;
                AMap.plugin("AMap.Weather", function () {
                    var wea = new AMap.Weather();

                    wea.getLive(result.city, function (err, data) {
                        // self.handleWeatherInfo(data);
                        console.log("test", data);
                        weather.value = data;
                    });
                });
            }
        });
    });
};
onMounted(() => {
    searchEairQuality();
    getLocation();
    now({
        location: "115.14,37.59",
        key: "618694ec377941278647ba1fcb7c7d51",
    }).then((res) => {
        console.log(res.data.now);
        if (res.data.now != null) {
            nowWeather.value = res.data.now;
        }
    });
});
</script>
<style lang="less" scoped>
.atmospheric-monitoring-dialog1 {
    width: 840px;
    height: 560px;
    border-radius: 4px;
    border: 1px solid rgba(71, 235, 235, 0.15);
    background: rgba(5, 32, 46, 0.6);
    backdrop-filter: blur(2px);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1003;
}
.atmospheric-monitoring-dialog2 {
    width: 840px;
    height: 448px;
    border-radius: 4px;
    border: 1px solid rgba(71, 235, 235, 0.15);
    background: rgba(5, 32, 46, 0.6);
    backdrop-filter: blur(2px);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1003;
}
.header {
    width: 840px;
    height: 52px;
    flex-shrink: 0;
    background: url("../../../../assets/images/dialog/header52.svg") no-repeat
        center center;
    background-size: cover;
    background-position: center;
    display: flex;
    .title {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 52px; /* 150% */
        margin-left: 32px;
    }
    .close {
        width: 24px;
        height: 24px;
        background: url("../../../../assets/images/dialog/close.svg") no-repeat
            center center;
        background-size: cover;
        background-position: center;
        margin: auto 0;
        margin-left: 698px;
    }
}
.container {
    width: 792px;
    height: 596px;
    padding: 16px 24px;
    .separate {
        width: 792px;
        height: 2px;
        background: url("../../../../assets/images/dialog/separate.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
        margin: 16px auto;
    }
    .btns {
        width: 792px;
        height: 40px;
        display: flex;
        gap: 48px;
        justify-content: center;
        color: #fff;
        text-align: center;
        text-shadow: 0px 0px 8px rgba(77, 57, 0, 0.6);
        font-family: Noto Sans SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 40px; /* 150% */
        .type-active-btn {
            width: 144px;
            height: 40px;
            background: url("../../../../assets/images/dialog/type-active-btn.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
        }
        .type-btn {
            width: 144px;
            height: 40px;
            background: url("../../../../assets/images/dialog/type-btn.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
        }
    }
    .monitor {
        .today {
            width: 792px;
            height: 111px;
            margin: 0 auto;
            margin-top: 31.68px;
            display: flex;
            //   gap: 15.84px;
            .img {
                width: 110px;
                height: 110px;
            }
            .top-liner-one {
                width: 2px;
                height: 95px;
                background: url("../../../../assets/images/card/top-liner-one1.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
                margin: auto 31.7px;
            }
            .top-liner {
                width: 2px;
                height: 95px;
                background: url("../../../../assets/images/card/top-liner-one1.svg")
                    no-repeat center center;
                background-size: cover;
                background-position: center;
                margin: auto 23.8px;
            }
            .big-text {
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 47.52px;
                font-style: normal;
                font-weight: 700;
                line-height: 64px; /* 133.333% */
            }
            .text {
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 27.72px;
                font-style: normal;
                font-weight: 400;
                line-height: 44px; /* 157.143% */
            }
            .text-right {
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 27.72px;
                font-style: normal;
                font-weight: 400;
                line-height: 44px; /* 157.143% */
            }
        }
        .interval {
            width: 792px;
            height: 47.5px;
            margin-top: 16px;
            background: url("../../../../assets/images/card/interval1.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
        }
        .prediction {
            width: 792px;
            height: 140px;
            margin-top: 51px;
            // background-color: aqua;
            display: flex;
            gap: 15.8px;
            .prediction-wrapper {
                display: flex;
                gap: 15.8px;
                .show {
                    width: 103px;
                    height: 140px;
                    .date {
                        color: #fff;
                        text-align: center;
                        font-family: Noto Sans SC;
                        font-size: 27.72px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 43.56px; /* 157.143% */
                    }
                    .img {
                        width: 48px;
                        height: 48px;
                        margin: 0 auto;
                        margin-top: 16px;
                    }
                    .temperature {
                        margin-top: 16px;
                        color: #fff;
                        text-align: center;
                        font-family: Noto Sans SC;
                        font-size: 23.76px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 35.64px; /* 150% */
                    }
                }
                .bottom-liner {
                    width: 2px;
                    height: 140px;
                    background: url("../../../../assets/images/card/weather-liner.svg")
                        no-repeat center center;
                    background-size: cover;
                    background-position: center;
                }
            }
        }
    }
    .analysis {
        width: 792px;
        height: 240px;
        // background-color: aqua;
        .linewrap {
            width: 792px;
            height: 240px;
        }
    }
}
</style>
