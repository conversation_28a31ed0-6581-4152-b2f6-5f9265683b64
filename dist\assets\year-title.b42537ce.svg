<svg width="317" height="22" viewBox="0 0 317 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M116.5 11.5H0.5V10.5H116.5V11.5Z" fill="url(#paint0_linear_234_88)"/>
<rect x="118.5" y="10" width="4" height="2" fill="#47EBEB"/>
<rect x="124.5" y="10" width="4" height="2" fill="#47EBEB"/>
<rect x="130.5" y="10" width="4" height="2" fill="#47EBEB"/>
<rect width="4" height="2" transform="matrix(-1 0 0 1 198.5 10)" fill="#47EBEB"/>
<rect width="4" height="2" transform="matrix(-1 0 0 1 192.5 10)" fill="#47EBEB"/>
<rect width="4" height="2" transform="matrix(-1 0 0 1 186.5 10)" fill="#47EBEB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M200.5 11.5H316.5V10.5H200.5V11.5Z" fill="url(#paint1_linear_234_88)"/>
<defs>
<linearGradient id="paint0_linear_234_88" x1="0.5" y1="11.0001" x2="175.5" y2="11.0001" gradientUnits="userSpaceOnUse">
<stop stop-color="#47EBEB" stop-opacity="0"/>
<stop offset="1" stop-color="#47EBEB"/>
</linearGradient>
<linearGradient id="paint1_linear_234_88" x1="316.5" y1="11.0001" x2="141.5" y2="11.0001" gradientUnits="userSpaceOnUse">
<stop stop-color="#47EBEB" stop-opacity="0"/>
<stop offset="1" stop-color="#47EBEB"/>
</linearGradient>
</defs>
</svg>
