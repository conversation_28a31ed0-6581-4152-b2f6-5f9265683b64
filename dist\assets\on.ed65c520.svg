<svg width="122" height="122" viewBox="0 0 122 122" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="122" height="122" rx="61" fill="#05202E" fill-opacity="0.3"/>
<rect width="122" height="122" rx="61" fill="url(#paint0_radial_130_4)" fill-opacity="0.15"/>
<circle cx="61" cy="61" r="52" transform="rotate(-45 61 61)" stroke="url(#paint1_angular_130_4)" stroke-width="1.5"/>
<g filter="url(#filter0_f_130_4)">
<circle cx="61" cy="61" r="41" fill="url(#paint2_radial_130_4)" fill-opacity="0.6"/>
</g>
<path d="M66 1L61 9L56 1H66Z" fill="url(#paint3_linear_130_4)"/>
<path d="M67 121L61 113L55 121H67Z" fill="url(#paint4_linear_130_4)"/>
<path d="M121 66L113 61L121 56L121 66Z" fill="url(#paint5_linear_130_4)"/>
<path d="M1 66L9 61L1 56L1 66Z" fill="url(#paint6_linear_130_4)"/>
<rect x="0.5" y="0.5" width="121" height="121" rx="60.5" stroke="#47EBEB" stroke-opacity="0.3"/>
<defs>
<filter id="filter0_f_130_4" x="9.75" y="9.75" width="102.5" height="102.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5.125" result="effect1_foregroundBlur_130_4"/>
</filter>
<radialGradient id="paint0_radial_130_4" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(61 61) rotate(90) scale(61)">
<stop stop-color="#30ABE8" stop-opacity="0"/>
<stop offset="1" stop-color="#30ABE8"/>
</radialGradient>
<radialGradient id="paint1_angular_130_4" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(61 61) rotate(90) scale(52)">
<stop stop-color="#47EBEB"/>
<stop offset="0.250085" stop-color="#47EBEB" stop-opacity="0"/>
<stop offset="0.500018" stop-color="#47EBEB"/>
<stop offset="0.749909" stop-color="#47EBEB" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint2_radial_130_4" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(61 61) rotate(90) scale(41)">
<stop stop-color="#47EBEB"/>
<stop offset="1" stop-color="#47EBEB" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_130_4" x1="61" y1="9" x2="61" y2="1" gradientUnits="userSpaceOnUse">
<stop stop-color="#47EBEB"/>
<stop offset="1" stop-color="#47EBEB" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_linear_130_4" x1="61" y1="113" x2="61" y2="121" gradientUnits="userSpaceOnUse">
<stop stop-color="#47EBEB"/>
<stop offset="1" stop-color="#47EBEB" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint5_linear_130_4" x1="113" y1="61" x2="121" y2="61" gradientUnits="userSpaceOnUse">
<stop stop-color="#47EBEB"/>
<stop offset="1" stop-color="#47EBEB" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint6_linear_130_4" x1="9" y1="61" x2="1" y2="61" gradientUnits="userSpaceOnUse">
<stop stop-color="#47EBEB"/>
<stop offset="1" stop-color="#47EBEB" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
