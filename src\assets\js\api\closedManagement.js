import { fetch } from "../request";
//出入园实时动态-车辆总数
export const security_car_io = (params) =>
    fetch("/api-v2/security_car_io", { ...params }, "POST");
//出入园实时动态-车辆总数
export const security_car_info = (params) =>
    fetch("/api-v2/security_car_info", { ...params }, "POST");
//车辆入园统计
export const security_car_statistics = (params) =>
    fetch("/api-v2/security_car_statistics", { ...params }, "POST");
//车辆违规记录
export const security_car_violation = (params) =>
    fetch("/api-v2/security_car_violation", { ...params }, "POST");
//危化品吨数
export const security_rwaste_ton = (params) =>
    fetch("/api-v2/security_rwaste_ton", { ...params }, "POST");
//卡口列表
export const nj_equipment_entrance_guard = (params) =>
    fetch("/api-v2/nj_equipment_entrance_guard", { ...params }, "POST");
//报警查询
export const alarmInfoIOC = (params) =>
    fetch("security/alarmInfo/alarmInfoIOC", { ...params }, "POST");
//报警查询
export const alarmRecordSelectPage = (params) =>
    fetch("/safety-v2/alarm-record/select-page", { ...params }, "POST");
//报警详情查询
export const alarmRecordDetail = (params) =>
    fetch(`/safety-v2/alarm-record/select-detail/${params.id}`, {}, "GET");
//删除报警处理
export const alarmRecordDelete = (params) =>
    fetch(`/safety-v2/alarm-record/delete/${params.id}`, {}, "DELETE");
//报警处理
export const alarmRecordhandleAlarm = (params) =>
    fetch(`/safety-v2/alarm-record/handleAlarm`, { ...params }, "POST");
//字典
export const typeList = (params) =>
    fetch(`/firecontrol/alarm/typeList`, { ...params }, "GET");
export const levelList = (params) =>
    fetch(`/firecontrol/alarm/levelList`, { ...params }, "GET");
//事件上报
export const handling = (params) =>
    fetch(`/firecontrol/alarm/handling`, { ...params }, "POST");
export const labelList = (params) =>
    fetch(`/firecontrol/alarm/labelList`, { ...params }, "GET");
//报警查询
export const getEnumList = (params) =>
    fetch("/safety-v2/alarm-record/getEnumList", { ...params }, "POST");
//报警查询-字典
export const security_dict = (params) =>
    fetch("/api-v2/security_dict", { ...params }, "POST");
//卡口-入园(1)/出园(0)
export const security_pass_records = (params) =>
    fetch("/api-v2/security_pass_records", { ...params }, "POST");
//卡口实时监控
export const cars_today_by_deviceId = (params) =>
    fetch("/api-v2/cars_today_by_deviceId", { ...params }, "POST");
//卡口详情
export const nj_equipment_entrance_guard_info = (params) =>
    fetch("/api-v2/nj_equipment_entrance_guard_info", { ...params }, "POST");
//卡口新-入园(1)/出园(0)
export const getPassRecord = (params) =>
    fetch("/security/carPassRecord/getPassRecord", { ...params }, "POST");

//新安防两侧面板
//出入园实时动态
export const inOutSituation = (params) => fetch(`/security/ioc/inOutSituation`, { ...params }, "GET");
//车辆入园统计
export const carStatistics = (params) => fetch(`/security/ioc/carStatistics`, { ...params }, "GET");
//封闭化管理/今日
export const securityToday = (params) => fetch(`/security/ioc/securityToday`, { ...params }, "GET");
//不安全行驶行为数量统计
export const countAlarm = (params) => fetch(`/security/ioc/alarm/countAlarm`, { ...params }, "GET");
//不安全行驶行为--分页
export const pageAlarm = (params) => fetch(`/security/ioc/alarm/pageAlarm`, { ...params }, "GET");
