<svg width="400" height="256" viewBox="0 0 400 256" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="400" height="256" rx="4" fill="#05202E" fill-opacity="0.3"/>
<rect x="134" y="239" width="4" height="2" fill="#30ABE8"/>
<rect x="140" y="239" width="4" height="2" fill="#30ABE8"/>
<rect x="146" y="239" width="4" height="2" fill="#30ABE8"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M132 240.5H16V239.5H132V240.5Z" fill="url(#paint0_linear_150_87)"/>
<rect width="4" height="2" transform="matrix(-1 0 0 1 266 239)" fill="#30ABE8"/>
<rect width="4" height="2" transform="matrix(-1 0 0 1 260 239)" fill="#30ABE8"/>
<rect width="4" height="2" transform="matrix(-1 0 0 1 254 239)" fill="#30ABE8"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M268 240.5H384V239.5H268V240.5Z" fill="url(#paint1_linear_150_87)"/>
<rect x="0.5" y="0.5" width="399" height="255" rx="3.5" stroke="#30ABE8" stroke-opacity="0.6"/>
<defs>
<linearGradient id="paint0_linear_150_87" x1="16" y1="240" x2="191" y2="240" gradientUnits="userSpaceOnUse">
<stop stop-color="#30ABE8" stop-opacity="0"/>
<stop offset="1" stop-color="#30ABE8"/>
</linearGradient>
<linearGradient id="paint1_linear_150_87" x1="384" y1="240" x2="209" y2="240" gradientUnits="userSpaceOnUse">
<stop stop-color="#30ABE8" stop-opacity="0"/>
<stop offset="1" stop-color="#30ABE8"/>
</linearGradient>
</defs>
</svg>
