<template>
    <div class="dialog-all-wrapper">
        <div class="duty-dialog-wrapper">
            <div class="duty-dialog border">
                <div class="dialog-header">
                    <div class="header-title">值班信息</div>
                    <img
                        src="@/assets/newImages/Dialog/dialog-close.svg"
                        class="header-close"
                        @click="closeDialog"
                    />
                </div>
                <div class="dialog-content">
                    <div class="content-item" v-for="(item,index) in tableList" :key="index">
                        <img class="duty-img" :src="item.picture"/>
                        <div class="duty-content">
                            <div class="duty-content-item">
                                <span class="duty-content-title">姓名：</span>{{item.dutyPerson}}
                            </div>
                            <div class="duty-content-item">
                                <span class="duty-content-title">手机号：</span>{{item.phone}}
                            </div>
                            <div class="duty-content-item">
                                <span class="duty-content-title">值班开始日期：</span>{{item.beginTimeDown}}
                            </div>
                            <div class="duty-content-item">
                                <span class="duty-content-title">值班结束日期：</span>{{item.overTimeDown}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="corner-bottom-left"></div>
        </div>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    computed,
    defineEmits,
} from "vue";
import { duty_list,dict_allList } from "@/assets/js/api/dialog/secureBase";

const emit = defineEmits(["closeDialog"]);

let tableList = ref([
]);
const getData = () =>{

    duty_list().then((res)=>{
        console.log(res.data.data.list,'res.data&&res.data.data');
        if(res.data&&res.data.data&&res.data.data.length>0){
            tableList.value=res.data.data;
            console.log(tableList,'tableList');
            
        }
    })
}
onMounted(() => {
    getData()
});
onMounted(() => {});
const closeDialog = () => {
    emit("closeDialog", "dutyDialogShow");
};
</script>

<style lang="less" scoped>
.dialog-all-wrapper {
    width: 100vw;
    height: 100vh;
    z-index: 1003;
    // background-color:antiquewhite;
    background-color: rgba(0, 0, 0, 0.5);

    position: absolute;
    top: 0px;
    left: 0px;
    .duty-dialog-wrapper {
        width: 800px;
        height: 560px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        // 添加底部两个装饰三角形
        &::after,
        .corner-bottom-left {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 6px;
            border-color: rgba(26, 159, 255, 0.6) transparent transparent
                transparent;
            z-index: 1;
        }

        // 左下角装饰三角形
        .corner-bottom-left {
            bottom: -7.5px;
            left: -6px;
            transform: rotate(45deg);
        }

        // 右下角装饰三角形
        &::after {
            bottom: -7.5px;
            right: -7.5px;
            transform: rotate(-45deg);
        }
    }
    .duty-dialog {
        width: 800px;
        height: 560px;

        background-color: rgba(5, 29, 46, 0.6);

        clip-path: polygon(
            10px 0,
            /* 左上角开始 */ calc(100% - 10px) 0,
            /* 右上角左侧 */ 100% 10px,
            /* 右上角下侧 */ 100% calc(100% - 10px),
            /* 右下角上侧 */ calc(100% - 10px) 100%,
            /* 右下角左侧 */ 10px 100%,
            /* 左下角右侧 */ 0 calc(100% - 10px),
            /* 左下角上侧 */ 0 10px /* 左上角下侧 */
        );
    }
    .dialog-header {
        width: 768px;
        height: 56px;
        padding: 0 16px;
        margin-left: -1px;
        background: url("@/assets/newImages/Dialog/dialog-header.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-title {
            font-family: MStiffHei PRC;
            font-weight: 400;
            font-size: 18px;
            line-height: 26px;
            letter-spacing: 0%;
            background: linear-gradient(
                    180deg,
                    rgba(26, 159, 255, 0.45) 20%,
                    rgba(26, 159, 255, 0) 80%
                ),
                linear-gradient(180deg, #ffffff 15.63%, #2ad9ff 87.5%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .header-close {
            width: 16px;
            height: 16px;
        }
    }
    .border {
        border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
        /* 使用渐变作为边框图像 */
        border-image-source: linear-gradient(
            360deg,
            rgba(26, 159, 255, 0.6) 0%,
            rgba(26, 159, 255, 0) 100%
        );
        border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
        border-image-width: 1px;
        border-top: none;
    }
    // 弹窗内部
    .dialog-content {
        width: 760px;
        height: 464px;
        padding: 20px;
        // display: flex;
        // flex-direction: column;
        // gap:20px;
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
        overflow-y:auto ;
        .content-item{
            width: 290px;
        height: 464px;
        padding:0 40px ;
        .duty-img{
            width: 214px;
height: 272px;

        }
        .duty-content{
            margin-top: 16px;
            .duty-content-item{
                width: 100%;
                height: 40px;;
                font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 40px;
text-align: left;
color: rgba(255, 255, 255, 1);
.duty-content-title{
    color: rgba(128, 234, 255, 1);

}
            }
        }
        }
      
    }
}
</style>
