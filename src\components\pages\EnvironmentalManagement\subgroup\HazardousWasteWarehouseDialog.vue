<template>
    <div class="dialog-all-wrapper">
        <div class="security-managemen-dialog-wrapper">
            <div class="security-managemen-dialog border">
                <div class="dialog-header">
                    <div class="header-title">仓库详情</div>
                    <img
                        src="@/assets/newImages/Dialog/dialog-close.svg"
                        class="header-close"
                         @click="closeDialog"
                    />
                </div>
                <div class="dialog-content">
                    
                    <div class="container">
            <div class="table">
                <table>
                    <tr>
                        <td class="title1">
                            <span class="title">仓库负责人</span>
                        </td>
                        <td class="title1">
                            <span class="title">仓库名称</span>
                        </td>
                        <td class="title1"><span class="title">容量</span></td>
                        <td class="title1">
                            <span class="title">贮存材料</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="value1">
                            <div class="value" :title="dialogData.person_name">
                                {{ dialogData.person_name }}
                            </div>
                        </td>
                        <td class="value1">
                            <div class="value" :title="dialogData.store_name">
                                {{ dialogData.store_name }}
                            </div>
                        </td>
                        <td class="value1">
                            <div class="value" :title="dialogData.memory_size">
                                {{ dialogData.memory_size }}
                            </div>
                        </td>
                        <td class="value1">
                            <div class="value" :title="dialogData.waste_name">
                                {{ dialogData.waste_name }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="title1">
                            <span class="title">联系方式</span>
                        </td>
                        <td class="title1">
                            <span class="title">已使用容量</span>
                        </td>
                        <td class="title1">
                            <span class="title">空余容量</span>
                        </td>
                        <td class="title1">
                            <span class="title"></span>
                        </td>
                    </tr>
                    <tr>
                        <td class="value1">
                            <div class="value" :title="dialogData.phone_number">
                                {{ dialogData.phone_number }}
                            </div>
                        </td>
                        <td class="value1">
                            <div class="value" :title="dialogData.memory_use">
                                {{ dialogData.memory_use }}
                            </div>
                        </td>
                        <td class="value1">
                            <div
                                class="value"
                                :title="dialogData.memory_no_use"
                            >
                                {{ dialogData.memory_no_use }}
                            </div>
                        </td>
                        <td class="value1">
                            <div class="value">{{}}</div>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="top-selects">
                            <div class="slect-tabs" ref="tabsRef">
                                <div class="tab-item" v-for="(item,index) in topTabs" :key="index" @click="selectType(item.value)">
                                    <div class="tab-item-text" :class="{'active-tab-item':currentTab === item.value}" >{{ item.label }}</div>
                                    <div class="tab-item-line" v-if="currentTab === item.value"></div>
                                </div>
                            </div>
                        </div>
            <div class="video" v-if="currentTab === 1">
                <div class="select">
                    <el-select
                        v-model="videoValue"
                        class="m-2"
                        placeholder="请选择"
                        @change="changeVideo"
                    >
                        <el-option
                            v-for="item in videoOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </div>
                <div ref="img" id="imgCCC" class="img">
                    <div v-if="videoGrey" class="img-no">暂无数据</div>
                </div>
            </div>
        </div>
                </div>
                
            </div>
            <div class="corner-bottom-left"></div>
        </div>
    </div>
</template>

<script setup>
import {
    getEquipmentInfo,
    getFlv,
} from "../../../../assets/js/api/safetySupervision";
import { waste_store_layer } from "../../../../assets/js/api/environmentalManagement";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    defineEmits,
    nextTick,
    toRef,
    isRef,
    toRefs,
} from "vue";
import * as echarts from "echarts";
import { setSize } from "../../../../assets/js/echartsSetSize";
import lodash from "lodash";
const img = ref(null);
var jessibuca = null;
const showWords = (content, length) => {
    if (content.length > length) {
        return content.substr(0, length) + "...";
    } else {
        return content;
    }
};
const { proxy } = getCurrentInstance();
const props = defineProps({
    pickId: {
        type: [Number, String],
    },
});
const dialogData = ref({
    store_name: "",
    memory_size: "",
    phone_number: "",
    phone_number: "",
    memory_use: "",
    memory_no_use: "",
});
const testObj = {
    name: "张三",
    age: "18",
};
//视频置灰
const videoGrey = ref(false);
const clickId = ref();
const { pickId } = toRefs(props);
watch(
    pickId,
    (a, b) => {
        console.log(a, b);
        console.log(dialogData);
        clickId.value = a;

        // emit("openDialog", 'majorHazar');

        waste_store_layer({ id: a }).then((res1) => {
            console.log(res1.data.data);
            let result = res1.data.data[0];
            dialogData.value = result;
            dialogData.value.memory_no_use =
                Number(dialogData.value.memory_size) -
                Number(dialogData.value.memory_use);

            getEquipmentInfo({
                equipmentTypeCode: "1592717090718388226",
                companyName: result.company_name,
                attributeValue: result.store_name,
            }).then((res2) => {
                if (
                    (res2.data.data && res2.data.data.length == 0) ||
                    res2.data.data == null ||
                    res2.data.data == undefined
                ) {
                    videoGrey.value = true;
                }
                if (
                    res2.data.success &&
                    res2.data.data &&
                    res2.data.data.length
                ) {
                    getFlv({
                        equipmentIdList: [res2.data.data[0].equipment_id],
                    }).then((res3) => {
                        // hazardDialog.value = true
                        // myDiv.style.display = "block";
                        jessibuca = new JessibucaPro({
                            container: img.value,
                            videoBuffer: 0.2, // 缓存时长
                            isResize: false,
                            decoder: "/Jessibuca/decoder-pro.js",
                            text: "",
                            loadingText: "加载中",
                            showBandwidth: true, // 显示网速
                            operateBtns: {
                                fullscreen: false,
                                screenshot: false,
                                play: false,
                                audio: false,
                                performance: false,
                            },
                            forceNoOffscreen: true,
                            isNotMute: false,
                            heartTimeout: 10,
                            ptzClickType: "mouseDownAndUp",
                        });
                        console.log(res3.data[0].flvAddress);
                        if (res3.data[0].flvAddress)
                            jessibuca.play(
                                proxy.$changeUrl(res3.data[0].flvAddress),
                            );
                    });
                }
            });
        });
    },
    {
        immediate: true,
    },
);
const destroy = () => {
    if (jessibuca) {
        // this.jessibuca=null

        jessibuca.destroy();
        jessibuca.value = null;
    }
};
onMounted(() => {
    // console.log(pickId);
});
const emit = defineEmits(["openDialog", "closeDialog"]);
// const typeActive = ref(1);
let option;
let linewrap = ref(null);
let lineChart;
let xData = ref([]);
let chartData = ref([]);
const initChart = () => {
    option = {
        xAxis: {
            type: "category",
            data: [
                "01:00",
                "02:00",
                "03:00",
                "04:00",
                "05:00",
                "06:00",
                "07:00",
            ],
            axisLine: {
                //  改变x轴颜色
                lineStyle: {
                    color: "#1AB2FF",
                },
            },
            axisLabel: {
                //  改变x轴字体颜色和大小
                textStyle: {
                    color: "#fff",
                    fontSize: 14,
                    lineHeight: 22,
                },
            },
            axisTick: {
                show: false,
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ["rgba(255, 255, 255, 0.15)"],
                    width: 1,
                    type: "solid",
                },
            },
        },
        yAxis: {
            type: "value",
            axisLine: {
                //  改变y轴颜色
                show: false,
                lineStyle: {
                    color: "#26D9FF",
                },
            },
            axisLabel: {
                //  改变y轴字体颜色和大小
                //formatter: '{value} m³ ', //  给y轴添加单位
                textStyle: {
                    color: "#fff",
                    fontSize: 14,
                    lineHeight: 22,
                },
            },
            axisTick: {
                show: false,
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ["#315070"],
                    width: 1,
                    type: "solid",
                },
            },
        },
        grid: {
            left: 10,
            right: 10,
            top: 40,
            bottom: 10,
            containLabel: true,
        },
        series: [
            {
                smooth: true,
                symbol: "circle",
                symbolSize: 7,
                markPoint: {
                    symbol: "circle",
                },
                markLine: {
                    symbol: "none",
                    label: {
                        normal: {
                            show: false,
                            color: "#fff",
                            backgroundColor: "rgba(228,0,54,70)",
                            fontSize: 16,
                            padding: 4,
                            borderRadius: 4,
                            show: true,
                            position: "start",
                            distance: 4,
                        },
                    },
                    lineStyle: {
                        type: "dotted",
                        color: "#FF4C4D",
                        width: 1,
                    },
                    data: [
                        {
                            yAxis: 400,
                        },
                        { yAxis: 220 },
                    ],
                },
                data: [220, 332, 601, 234, 490, 730, 590],
                type: "line",
                itemStyle: {
                    normal: {
                        label: {
                            show: true,
                            color: "#fff",
                            fontSize: 12,
                        },
                        color: "#294E8F",
                        borderColor: "3D7EEB",
                        borderWidth: 2,
                    },
                },
                lineStyle: {
                    normal: {
                        width: 2,
                        color: "#1AB2FF",
                        // shadowColor: "#3D7EEB",
                        // shadowBlur: 10
                    },
                },
                areaStyle: {
                    color: {
                        type: "linear",
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0.5,
                                color: "rgba(26, 178, 255, 0.30)", // 0% 处的颜色
                            },
                            {
                                offset: 1,
                                color: "rgba(26, 178, 255, 0.00)", // 100% 处的颜色
                            },
                        ],
                        global: false, // 缺省为 false
                    },
                },
            },
        ],
    };
};
const tableData = [
    {
        num: 1,
        number: "N45236",
        name: "检测设备1",
        enterprise: "河北六合化工有限公司",
        factor: "CO2",
        value: "12",
        range: "0-10",
        exceed: "1",
        time: "2023-08-20 12:01:22",
    },
    {
        num: 2,
        number: "N56392",
        name: "检测设备2",
        enterprise: "河北六合化工有限公司",
        factor: "CO",
        value: "9",
        range: "0-5",
        exceed: "2",
        time: "2023-08-24 21:56:01",
    },
    {
        num: 4,
        number: "N85692",
        name: "检测设备12",
        enterprise: "河北六合化工有限公司",
        factor: "NO",
        value: "2",
        range: "0-1",
        exceed: "1",
        time: "2023-08-25 07:24:52",
    },
];
//顶部tab
let topTabs = ref([
    {
        value:1,
        label:'实时视频'
    }
]);
let currentTab = ref(1);
const tabsRef = ref(null);
const selectType = (type) => {
        currentTab.value = type;

    if (type === 1) {
        major_danger_source_info({ id: clickId.value }).then((res1) => {
            let result = res1.data.data[0];
            getEquipmentInfo({
                equipmentTypeCode: "1592717090718388226",
                companyName: result.company,
                attributeValue: result.area_name,
            }).then((res2) => {
                getFlv({
                    equipmentIdList: [res2.data.data[0].equipment_id],
                }).then((res3) => {
                    dialogData.value = result;
                    // hazardDialog.value = true
                    // myDiv.style.display = "block";
                    jessibuca = new JessibucaPro({
                        container: img.value,
                        videoBuffer: 0.2, // 缓存时长
                        isResize: false,
                        decoder: "/Jessibuca/decoder-pro.js",
                        text: "",
                        loadingText: "加载中",
                        showBandwidth: true, // 显示网速
                        operateBtns: {
                            fullscreen: false,
                            screenshot: false,
                            play: false,
                            audio: false,
                            performance: false,
                        },
                        forceNoOffscreen: true,
                        isNotMute: false,
                        heartTimeout: 10,
                        ptzClickType: "mouseDownAndUp",
                    });
                    console.log(res3.data[0].flvAddress);
                    if (res3.data[0].flvAddress)
                        jessibuca.play(res3.data[0].flvAddress);
                });
            });
        });
    } else if (type === 2) {
        // chartData = []
        //     xData = []
        //     xData = [
        //         {name: '氯碱化工', value: 5},
        //         {name: '香精香料', value: 2},
        //         {name: '食品添加剂', value: 4},
        //         {name: '医药中间体', value: 3},
        //         {name: '化工新材料', value: 13},
        //     ]
        //     xData.forEach((item) => {
        //         console.log(item.value);
        //         chartData.push(item.value)
        //     })
        //     console.log(chartData);
        // chartData = [70,42,63,84,75,34]
        lineChart = echarts.init(linewrap.value);
        initChart();
        lineChart.setOption(option);
        window.addEventListener("resize", () => {
            lineChart.resize();
        });
    }
};
const monitorActive = ref(1);
const selectMonitorType = (x) => {
    console.log(x);
    monitorActive.value = x;
};
// const innerActive = ref(1)
// watch(innerActive,(a,b)=>{
// console.log(a,b);
//   if(a==1){
//     emit("sizeChange", 6);

//   }else if(a==2){
//     emit("sizeChange", 5);

//   }else if(a==3){
//     emit("sizeChange", 4);

//   }
// })
// const selectInnerType = (type) => {
//   // size.value =type

//   innerActive.value = type
//   if(type==1){
//     size.value = 1
//   }else if(type==1){
//     size.value =2

//   }
// }
onBeforeUnmount(() => {
    destroy();
});
const closeDialog = () => {
    console.log("warehouse");
    emit("closeDialog", "warehouse");
};
</script>
<style lang="less" scoped>
.dialog-all-wrapper {
    width: 100vw;
    height: 100vh;
    z-index: 1003;
    // background-color:antiquewhite;
    background-color: rgba(0, 0, 0, 0.5);

    position: absolute;
    top: 0px;
    left: 0px;
    .security-managemen-dialog-wrapper {
        // width: 1160px;
        // height: 720px;
        width: 840px;
    height: 780px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        // 添加底部两个装饰三角形
        &::after,
        .corner-bottom-left {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 6px;
            border-color: rgba(26, 159, 255, 0.6) transparent transparent
                transparent;
            z-index: 1;
        }

        // 左下角装饰三角形
        .corner-bottom-left {
            bottom: -7.5px;
            left: -6px;
            transform: rotate(45deg);
        }

        // 右下角装饰三角形
        &::after {
            bottom: -7.5px;
            right: -7.5px;
            transform: rotate(-45deg);
        }
    }
    .security-managemen-dialog {
        width: 840px;
    height: 780px;

        background-color: rgba(5, 29, 46, 0.6);

        clip-path: polygon(
            10px 0,
            /* 左上角开始 */ calc(100% - 10px) 0,
            /* 右上角左侧 */ 100% 10px,
            /* 右上角下侧 */ 100% calc(100% - 10px),
            /* 右下角上侧 */ calc(100% - 10px) 100%,
            /* 右下角左侧 */ 10px 100%,
            /* 左下角右侧 */ 0 calc(100% - 10px),
            /* 左下角上侧 */ 0 10px /* 左上角下侧 */
        );
    }
    .dialog-header {
        width: 808px;
        height: 56px;
        padding: 0 16px;
        margin-left: -1px;
        background: url("@/assets/newImages/Dialog/dialog-header.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-title {
            font-family: MStiffHei PRC;
            font-weight: 400;
            font-size: 18px;
            line-height: 26px;
            letter-spacing: 0%;
            background: linear-gradient(
                    180deg,
                    rgba(26, 159, 255, 0.45) 20%,
                    rgba(26, 159, 255, 0) 80%
                ),
                linear-gradient(180deg, #ffffff 15.63%, #2ad9ff 87.5%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .header-close {
            width: 16px;
            height: 16px;
        }
    }
    .border {
        border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
        /* 使用渐变作为边框图像 */
        border-image-source: linear-gradient(
            360deg,
            rgba(26, 159, 255, 0.6) 0%,
            rgba(26, 159, 255, 0) 100%
        );
        border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
        border-image-width: 1px;
        border-top: none;
    }
    // 弹窗内部
    .dialog-content {
        width: 800px;
        height: 684px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap:20px;
        // background-color: #2ad9ff;
        
    }
}

.container {
    // width: 792px;
    // height: 728px;
    // padding: 16px 24px;
    // background-color: aquamarine;
    .table {
        width: 792px;
        height: 144px;
        // background-color: antiquewhite;
        table {
            border-collapse: collapse;
        }
        table td {
            border-style: solid;
            border-width: 1px;
            border-color: #30abe8;
        }
        .title1 {
            width: 174px;
            height: 36px;
            padding: 0 12px;
            color: #47ebeb;
            text-align: left;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            background-color: rgba(48, 171, 232, 0.1);
        }
        .value1 {
            width: 174px;
            height: 36px;
            padding: 0 12px;
            color: #fff;
            text-align: justify;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            .value {
                width: 174px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
    .separate {
        width: 792px;
        height: 2px;
        background: url("../../../../assets/images/dialog/separate.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
        margin: 16px auto;
    }
    .top-selects{
            width: 8000px;
            height: 48px;
            border-bottom: 1px solid rgba(128, 234, 255, 0.3);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .slect-tabs {
            width: 800px;
            height: 48px;
            display: flex;
            align-items: center;
            gap: 20px;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            
            /* 隐藏滚动条 - webkit浏览器 */
            &::-webkit-scrollbar {
                display: none;
            }
            
            /* 隐藏滚动条 - Firefox */
            scrollbar-width: none;
            
            /* 隐藏滚动条 - IE */
            -ms-overflow-style: none;
            
            .tab-item {
                height: 36px;
                padding-top: 12px;
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;
                text-align: center;
                color: rgba(128, 234, 255, 1);
                white-space: nowrap; /* 防止文字换行 */
                flex-shrink: 0; /* 防止项目被压缩 */
            }
            .tab-item-line {
                width: 24px;
                height: 2px;
                background-color: rgba(255, 198, 26, 1);
                margin: auto;
                margin-top: 10px;
            }
            .active-tab-item {
                color: rgba(255, 198, 26, 1);
            }
        }
    .video {
        width: 792px;
        height: 484px;
        margin-top: 16px;
        .select {
            width: 400px;
            height: 24px;
            // margin-left: 278px;
            :deep(.el-select) {
                width: 144px;
                height: 24px !important;
                color: #ffffff;
            }
            :deep(.el-input__wrapper) {
                background-color: rgba(26, 178, 255, 0.3);
                // border: 1px solid #1A9FFF !important;
                // border-radius: 4px;
                height: 24px !important;
            }
            :deep(.el-input__inner) {
                // background-color: rgba(26, 178, 255, 0.30);
                color: #ffffff;
                height: 24px !important;
            }
        }
        .img {
            width: 792px;
            height: 444px;
            margin-top: 16px;
            background: rgba(48, 171, 232, 0.15);
            display: flex;
            justify-content: center;
            .img-no {
                margin: auto 0;
                color: #fff;
                text-align: center;
                font-family: "Noto Sans SC";
                font-size: 20px;
                font-style: normal;
                font-weight: 400;
                line-height: 28px; /* 140% */
            }
        }
    }
    .monitor-data {
        width: 792px;
        height: 354px;
        // background: rgba(48, 171, 232, 0.15);
        margin-top: 16px;
        .monitor-btns {
            width: 792px;
            height: 28px;
            background: url("../../../../assets/images/dialog/emergency-archives-liner.svg")
                no-repeat center center;
            background-size: cover;
            background-position: center;
            .btns {
                width: 320px;
                height: 28px;
                margin: 0 auto;
                display: flex;
                justify-content: space-between;
                color: #fff;
                text-align: center;
                text-shadow: 0px 0px 8px rgba(8, 48, 69, 0.6);
                font-family: Noto Sans SC;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 28px; /* 133.333% */
                .monitor-active-btn {
                    width: 96px;
                    height: 28px;
                    background: url("../../../../assets/images/dialog/device-active-btn.svg")
                        no-repeat center center;
                    background-size: cover;
                    background-position: center;
                }
                .monitor-btn {
                    width: 96px;
                    height: 28px;
                    background: url("../../../../assets/images/dialog/device-btn.svg")
                        no-repeat center center;
                    background-size: cover;
                    background-position: center;
                }
            }
            .linewrap {
                width: 792px;
                height: 310px;
                margin-top: 16px;
            }
        }
    }
    .alarm {
        width: 792px;
        height: 274px;
        // background: rgba(48, 171, 232, 0.15);
        margin-top: 16px;

        .tablebox {
            //表格四个边框的颜色
            border: 1px solid #30abe8 !important;
            th.el-table__cell {
                border: none !important;
            }
        }
        :deep(.el-table),
        :deep(.el-table__expanded-cell) {
            background-color: transparent !important;
            color: #ffffff;
        }

        // 设置表头颜色
        :deep(.el-table th) {
            border-bottom: 1px solid #30abe8 !important;
            height: 36px;
            background-color: rgba(48, 171, 232, 0.1) !important;
            color: #47ebeb;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            // border-color: #30ABE8 !important;
        }
        .el-table__header-wrapper,
        .el-table__fixed-header-wrapper {
            //表头的样式
            th {
                border-bottom: 1px solid #30abe8 !important;
                border-right: 1px solid #30abe8 !important;
            }
        }
        /* 表格内背景颜色 */

        :deep(.el-table) tr,
        :deep(.el-table) td {
            background-color: transparent !important;
            // border-color: #30ABE8 !important;
        }
        :deep(.el-table) tr td {
            background-color: transparent !important;
            height: 58px;
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            letter-spacing: -0.28px;
            // border-color: #30ABE8 !important;
        }
        :deep(.el-table__cell) {
            border-right: 1px solid #30abe8 !important ;
            border-bottom: 1px solid #30abe8 !important  ;
        }
        /* 去掉table 最底部的线 */
        :deep(.el-table__inner-wrapper::before) {
            left: 0;
            bottom: 0;
            width: 100%;
            height: 0;
            z-index: 3;
        }
        .pagination {
            margin-top: 16px;
        }
        :deep(.el-pagination .el-pager li:not(.disabled)) {
            background-color: transparent; /*进行修改未选中背景和字体*/
            color: #30abe8;
            text-align: center;
            border-radius: 2px;
            border: 1px solid #30abe8;
            /* Body/regular */
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }

        :deep(.el-pagination .el-pager li:not(.disabled).active) {
            // background-color:green;/*进行修改选中项背景和字体*/
            // color:#fff;
        }

        :deep(.el-pagination .el-pager li:hover) {
            border-radius: 2px;
            border: 1px solid #47ebeb;
            background: linear-gradient(
                180deg,
                rgba(71, 235, 235, 0) 50%,
                rgba(71, 235, 235, 0.45) 100%
            );
            color: #47ebeb;
            text-align: center;

            /* Body/regular */
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
        :deep(.el-pagination .btn-next) {
            background-color: transparent; /*进行修改未选中背景和字体*/
            color: #30abe8;
            text-align: center;
            border-radius: 2px;
            border: 1px solid #30abe8;
            /* Body/regular */
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
        :deep(.el-pagination .btn-prev) {
            background-color: transparent; /*进行修改未选中背景和字体*/
            color: #30abe8;
            text-align: center;
            border-radius: 2px;
            border: 1px solid #30abe8;
            /* Body/regular */
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
        :deep(.el-pagination__total) {
            background-color: transparent; /*进行修改未选中背景和字体*/
            color: #30abe8;
            text-align: center;
            /* Body/regular */
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
    }
}
</style>
