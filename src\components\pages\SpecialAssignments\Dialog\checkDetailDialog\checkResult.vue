<template>
  <div class="check-detail-wrapper">
    <div class="check-item">
        <div class="check-item-title">检查人：</div>
        <div class="check-item-value">{{checkObj&&checkObj.checker?checkObj.checker:'-'}}</div>
    </div>
    <div class="check-item">
        <div class="check-item-title">检查时间：</div>
        <div class="check-item-value">{{checkObj&&checkObj.checkTime?checkObj.checkTime:'-'}}</div>
    </div>
    <div class="check-item">
        <div class="check-item-title">安全措施落实：</div>
        <div class="check-item-value">
            <el-radio-group v-model="checkObj.measureScore">
                <el-radio :disabled="true" v-for="item in radioArr" :key="item" :label="item">{{item}}</el-radio>
            </el-radio-group>
  </div>
    </div>
    <div class="check-item">
        <div class="check-item-title">风险辨识准确度：</div>
        <div class="check-item-value">
            <el-radio-group v-model="checkObj.identifyScore">
                <el-radio :disabled="true" v-for="item in radioArr" :key="item" :label="item">{{item}}</el-radio>
            </el-radio-group>
        </div>
    </div>
    <div class="check-item">
        <div class="check-item-title">现场情况：</div>
        <div class="check-item-value">
            <el-radio-group v-model="checkObj.liveScore">
                <el-radio :disabled="true" v-for="item in radioArr" :key="item" :label="item">{{item}}</el-radio>
            </el-radio-group></div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed, defineEmits, toRefs } from "vue";
import { check_detail } from "@/assets/js/api/dialog/specialAssignments";

const emit = defineEmits(["closeDialog"]);
const props = defineProps({
    checkObj: {
        type: [Object],
    },
});
const radioArr = ref([10,8,6,4,2,0])
const { checkObj } = toRefs(props);
onMounted(()=>{
    console.log(checkObj.value,'详情---------');
    
})
</script>

<style lang="less" scoped>
.check-detail-wrapper{
    width: 760px;
    height: 584px;
    padding-top: 20px;
    .check-item{
        width: 760px;
    height: 40px;
    font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: rgba(255, 255, 255, 1);
display: flex;
align-items: center;
    .check-item-title{
color: rgba(128, 234, 255, 1);

    }
    .check-item-value{
    }
    }
}
</style>