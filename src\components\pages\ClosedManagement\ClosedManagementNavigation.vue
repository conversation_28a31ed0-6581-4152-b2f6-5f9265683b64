<template>
    <div>
        <div class="input-wrapper">
            <el-date-picker
                class="m-2"
                @change="changeDate"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                v-model="value2"
                type="date"
                :clearable="false"
            />
            <!--      <el-time-picker-->
            <!--          class="m-2"-->
            <!--          v-model="value1"-->
            <!--          @change="changeTime"-->
            <!--          format="HH:MM:ss"-->
            <!--          value-format="HH:MM:ss"-->
            <!--          is-range-->
            <!--          range-separator="~"-->
            <!--          start-placeholder="开始时间"-->
            <!--          end-placeholder="结束时间"-->
            <!--      />-->
            <el-select
                v-model="carPlate"
                class="m-2"
                placeholder="请选择"
                clearable
                @change="changeEnterpriseName"
                filterable
            >
                <el-option
                    v-for="item in carPlateOptions"
                    :key="item"
                    :label="item"
                    :value="item"
                />
            </el-select>
            <div>
                <img
                    @click="reset"
                    src="../../../assets/images/closed/reset.svg"
                    style="width: 20px; height: 20px"
                    alt=""
                />
            </div>
        </div>
        <div class="closed-management-navigation" v-if="showFlag != 1">
            <div
                :class="active === 1 ? 'active-btn' : 'btn'"
                @click="setPage(1)"
            >
                <img
                    class="icon"
                    src="../../../assets/images/icon/bayonet.svg"
                />
                <div class="text">卡口</div>
            </div>
            <div
                :class="active === 2 ? 'active-btn' : 'btn'"
                @click="setPage(2)"
            >
                <img class="icon" src="../../../assets/images/icon/video.svg" />
                <div class="text">视频</div>
            </div>
            <div
                :class="active === 3 ? 'active-btn' : 'btn'"
                @click="setPage(3)"
            >
                <img
                    class="icon"
                    src="../../../assets/images/icon/monitor.svg"
                />
                <div class="text">全方位监控</div>
            </div>
        </div>
        <bayonet-dialog
            v-if="bayonetDialogShow"
            :pickId="bayonetId"
            :positionData="positionData"
            :equipmentId="equipmentId"
            @closeDialog="closeDialog"
        ></bayonet-dialog>
        <infrastructure-dialog
            :pickId="videoId"
            v-if="videoDialogShow"
            @closeDialog="closeDialog"
        ></infrastructure-dialog>
        <car-detail-dialog
            :pickId="carId"
            v-if="carDialogShow"
            @closeDialog="closeDialog"
        ></car-detail-dialog>
        <car-data-detail-dialog
            :pickId="carData"
            v-if="carDataDialogShow"
            @closeDialog="closeDialog"
        ></car-data-detail-dialog>
        <departure-dialog
            :departureData="departureData"
            v-if="departureShow"
            @closeDialog="closeDialog"
        >
        </departure-dialog>
    </div>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    onUnmounted,
    getCurrentInstance,
    watch,
} from "vue";
import {
    nj_equipments,
    nj_equipment_park,
} from "../../../assets/js/api/parkArchives";
import { nj_equipment_entrance_guard } from "../../../assets/js/api/closedManagement";
import {
    alarmInfoPersonRecord,
    cameraPersonLink,
    carGpsPoint,
    getDictByType,
    gpsCarPlate,
    gpsLineByTimeCarPlate,
    nj_iocGateC,
    se_gpsCarPlate,
    se_gpsLineByTimeCarPlate,
    detail,
} from "../../../assets/js/api/comprehensiveSupervision";
import bayonetDialog from "./subgroup/bayonetDialog.vue";
import videoDialog from "./subgroup/videoDialog.vue";
import infrastructureDialog from "./subgroup/infrastructureDialog.vue";
import url from "../../../assets/images/closed/car_success.svg";

const showFlag = ref(1);
const active = ref(0);
const value1 = ref("");
const value2 = ref("");
const carPlate = ref(null);
import aliveImg from "../../../assets/images/card/icon1.png";
import * as Cesium from "cesium";

const begin = ref(null);
const end = ref(null);
const { proxy } = getCurrentInstance();
const carPlateOptions = ref([]);
// const aliveImg = ref('../../../assets/images/card/interval.svg')
const videoId = ref(null);
const carId = ref(null);
const carData = ref(null);
const Data = ref(null);
const bayonetId = ref(null);
const equipmentId = ref(null);
const positionData = ref([]);
const project = ref([]);
const actual = ref([]);
const pointList = ref([]);
const limitData = ref(null);
const trueData = ref(null);
const dialogData = ref({
    carPlate: "",
    goodsName: "",
    goodsWeight: "",
    goodsType: "",
    driverName: "",
    driverPhone: "",
    bevisitCompany: "",
    bevisitPerson: "",
    bevisitNumber: "",
    planTime: "",
});
watch(active, (a, b) => {
    console.log(a, b);
    if (active.value == 0) {
        quitOnePost();
        initCarMap();
    } else {
        console.log(222);
        window.clearInterval(timer.value);
        timer.value = null;
    }
});

const changeEnterpriseName = (e) => {
    if (e) {
        se_gpsLineByTimeCarPlate({
            beginTime: begin.value,
            endTime: end.value,
            carPlate: e,
        }).then((res) => {
            window.map1.clearMap();
            initBoundary();
            window.clearInterval(timer.value);
            timer.value = null;
            let lat = [];
            let path = [];
            console.log(res.data.data.carGpsRecordList);
            if (res.data.data.carGpsRecordList != []) {
                res.data.data.carGpsRecordList.forEach((long) => {
                    lat.push([long.lng, long.lat]);
                });
                path.push(lat);
            }

            if (
                res.data.data.drivePlan != null &&
                res.data.data.drivePlan.route != null
            )
                project.value = eval(res.data.data.drivePlan.route);
            else project.value = [];
            limitData.value = res.data.data.drivePlan;
            pointList.value = res.data.data.carGpsRecordList;
            trueData.value = res.data.data;
            actual.value = path;
            if (res.data.data.heavyTruckEdit != null)
                dialogData.value = res.data.data.heavyTruckEdit;
            initPathSimplifier();
        });
    }
};
const dealWith = (e) => {
    return Number(e.toString().substring(0, 5));
};
const initPathSimplifier = () => {
    let path = actual.value[0];
    let marker = new AMap.Marker({
        icon: url,
        position: [path[0][0], path[0][1]],
        offset: new AMap.Pixel(-23, -60),
        exData: {
            data: dialogData.value,
            trueData: trueData.value,
        },
    });
    marker.on("click", (e) => {
        let car = e.target._opts.exData.trueData.heavyTruck;
        // console.log(e.target._opts.exData.trueData.heavyTruck)
        carId.value = car.carPlate;
        carDialogShow.value = true;
        proxy.$loading.show();
        // window.map1.setZoomAndCenter(22, e.target._opts.position)
        console.log(carId.value);
    });
    marker.setMap(window.map1);
    let lineArr = []; // 存储轨迹的一维数组
    let mapList = new Map();
    let arr = [];
    let arr1 = [];
    let arr2 = [];
    console.log(pointList);
    pointList.value.forEach((pos, index) => {
        let marker1 = new AMap.CircleMarker({
            center: [pos.lng, pos.lat], //圆心
            radius: 10.1, //半径
            strokeColor: "white", //轮廓线颜色
            strokeWeight: 0.1, //轮廓线宽度
            strokeOpacity: 0, //轮廓线透明度
            fillColor: "rgba(255,255,255,0)", //多边形填充颜色
            fillOpacity: 0, //多边形填充透明度
            zIndex: 199, //多边形覆盖物的叠加顺序
            cursor: "pointer", //鼠标悬停时的鼠标样式
            exData: {
                data: trueData.value,
            },
        });
        marker1.on("mouseover", (e) => {
            let item = e.target._opts.exData.data;
            console.log(item);
            let speedLimit;
            if (item.drivePlan == null) speedLimit = 999;
            else speedLimit = item.drivePlan.speedLimit;
            if (pos.speed > speedLimit) {
                window.infoWindowone = new AMap.InfoWindow({
                    position: [pos.lng, pos.lat],
                    offset: new AMap.Pixel(0, 0),
                    content: `<div style="background: #fff;padding:10px">
                超速：${pos.speed}km/h,参考值${speedLimit}km/h
            </div>`,
                });
                window.infoWindowone.open(window.map1);
            }
        });
        window.map1.add(marker1);
        let limit_1;
        if (limitData.value == null) {
            limit_1 = 999;
        } else {
            limit_1 = limitData.value.speedLimit;
        }
        console.log(dealWith(pos.lng));
        console.log(
            project.value.map((e) => dealWith(e[0])),
            pos,
        );
        if (
            project.value.length != 0 &&
            (!project.value
                .map((e) => dealWith(e[0]))
                .includes(dealWith(pos.lng)) ||
                !project.value
                    .map((e) => dealWith(e[1]))
                    .includes(dealWith(pos.lat)))
        ) {
            if (arr2.length != 0) {
                mapList.set(`${index}_success_`, arr2);
                arr2 = [];
            }
            if (arr.length != 0) {
                mapList.set(`${index}_speed_`, arr);
                arr = [];
            }
            arr1.push([pos.lng, pos.lat]);
        } else if (pos.speed > limit_1) {
            if (arr2.length != 0) {
                mapList.set(`${index}_success_`, arr2);
                arr2 = [];
            }
            if (arr1.length != 0) {
                mapList.set(`${index}_boundary_`, arr1);
                arr1 = [];
            }
            arr.push([pos.lng, pos.lat]);
        } else {
            if (arr.length != 0) {
                mapList.set(`${index}_speed_`, arr);
                arr = [];
            }
            if (arr1.length != 0) {
                mapList.set(`${index}_boundary_`, arr1);
                arr1 = [];
            }
            arr2.push([pos.lng, pos.lat]);
        }
        if (index == pointList.value.length - 1) {
            if (arr.length != 0) {
                mapList.set(`${index}_speed_`, arr);
                arr = [];
            }
            if (arr1.length != 0) {
                mapList.set(`${index}_boundary_`, arr1);
                arr1 = [];
            }
            if (arr2.length != 0) {
                mapList.set(`${index}_success_`, arr2);
                arr2 = [];
            }
        }
    });
    let objKeyArr = Array.from(mapList.keys());
    for (let i = 0; i < objKeyArr.length; i++) {
        if (i < objKeyArr.length - 1) {
            mapList.get(objKeyArr[i]).push(mapList.get(objKeyArr[i + 1])[0]);
        }
    }
    console.log(mapList);
    mapList.forEach((key, i) => {
        let color = ""; // 定义轨迹的颜色变量
        let type = i.split("_"); // 获取轨迹的类型
        if (type[1] == "speed") {
            // 根据轨迹的类型进行颜色的赋值
            color = "#ff8119";
        } else if (type[1] == "boundary") {
            color = "#ff0000";
        } else if (type[1] == "success") {
            color = "#AF5";
        }
        // 配置轨迹
        name = new AMap.Polyline({
            map: window.map1,
            path: key, // 轨迹的坐标数组
            showDir: true,
            strokeColor: color,
            strokeWeight: 6, //线宽
            lineJoin: "round",
        });
        window.map1.add([name]);
        lineArr = lineArr.concat(key);
    });
    // })
    var passedPolyline = new AMap.Polyline({
        map: window.map1,
        strokeColor: "#AF5", //线颜色
        strokeWeight: 6, //线宽
    });
    console.log(lineArr);
    marker.moveAlong(lineArr, {
        duration: 500, //可根据实际采集时间间隔设置
        autoRotation: false,
    });
    // marker.on("moving", function (e) {
    //   passedPolyline.setPath(e.passedPath)
    //   map.setCenter(e.target.getPosition())
    // })
    // })
    // window.map1.setFitView()
};
const reset = () => {
    value1.value = null;
    value2.value = null;
    carPlate.value = null;
    begin.value = null;
    end.value = null;
    window.map1.clearMap();
    window.clearInterval(timer.value);
    timer.value = null;
    initBoundary();
    initCarMap();
    quitOnePost();
};
const changeDate = (e, c) => {
    console.log(e, "进入changeData");
    if (e) {
        se_gpsCarPlate({
            beginTime: `${value2.value} 00:00:00`,
            endTime: `${value2.value} 24:00:00`,
        }).then((res) => {
            begin.value = `${value2.value} 00:00:00`;
            end.value = `${value2.value} 24:00:00`;
            carPlateOptions.value = res.data.data;
        });
    } else {
        begin.value = null;
        end.value = null;
    }
};
const changeTime = (e, c) => {
    if (e) {
        se_gpsCarPlate({
            beginTime: `${value2.value} ${e[0]}`,
            endTime: `${value2.value} ${e[1]}`,
        }).then((res) => {
            begin.value = `${value2.value} ${e[0]}`;
            end.value = `${value2.value} ${e[1]}`;
            carPlateOptions.value = res.data.data;
        });
    } else {
        begin.value = null;
        end.value = null;
    }
};
const setPage = (type) => {
    if (active.value == type) {
        active.value = 0;
        window.viewer.entities.removeAll(); //删除所有
        window.map1.clearMap();
        console.log(window.viewer.entities);
        initBoundary();
    } else {
        active.value = type;
        window.viewer.entities.removeAll(); //删除所有
        window.map1.clearMap();
        initBoundary();
        if (type == 1) {
            positionData.value = [];
            nj_iocGateC({ "": "" }).then((res) => {
                if (res.data.success && res.data.data && res.data.data.length) {
                    console.log(res.data.data);
                    // positionData.value = res.data.data
                    res.data.data.forEach((item) => {
                        if (item.longitude != null && item.latitude != null) {
                            positionData.value.push({
                                name: item.equipment_id,
                                id: item.id,
                                cameraId: item.cameraId,
                                longitude:
                                    item.longitude == null
                                        ? null
                                        : Number(item.longitude),
                                latitude:
                                    item.latitude == null
                                        ? null
                                        : Number(item.latitude),
                            });
                        }
                    });
                    if (window.toggle == 2) {
                        putIcons(positionData.value, bayonetImg);
                        console.log(window.viewer.entities);
                        // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                        window.viewer.screenSpaceEventHandler.setInputAction(
                            function (e) {
                                var pick = viewer.scene.pick(e.position);
                                if (pick && pick.id) {
                                    console.log(pick.id);
                                    equipmentId.value = pick.id.name;
                                    bayonetId.value = pick.id.id;
                                    bayonetDialogShow.value = true;
                                    proxy.$loading.show();
                                    window.viewer.camera.setView({
                                        destination:
                                            Cesium.Cartesian3.fromDegrees(
                                                pick.id._longitude,
                                                pick.id._latitude,
                                                800,
                                            ),
                                    });
                                }
                            },
                            Cesium.ScreenSpaceEventType.LEFT_CLICK,
                        );
                    } else if (window.toggle == 3) {
                        console.log(window.map1, positionData.value);
                        positionData.value.forEach((item) => {
                            let marker = new AMap.Marker({
                                // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                                icon: new AMap.Icon({
                                    size: new AMap.Size(72, 92), // 图标尺寸
                                    image: "/static/poi/bayonet-new.svg", //绝对路径
                                    imageSize: new AMap.Size(72, 92),
                                }),
                                position: [item.longitude, item.latitude],
                                offset: new AMap.Pixel(-36, -92),
                            });
                            marker.on("click", (e) => {
                                equipmentId.value = item.equipment_id;
                                bayonetId.value = item.name;
                                bayonetDialogShow.value = true;
                                proxy.$loading.show();
                                window.map1.setZoomAndCenter(
                                    22,
                                    e.target._opts.position,
                                );

                                console.log(bayonetId.value);
                            });
                            marker.setMap(window.map1);
                        });
                        window.map1.setFitView();
                    }
                }
            });
        } else if (type === 2) {
            positionData.value = [];

            nj_equipment_park().then((res) => {
                if (res.data.success && res.data.data && res.data.data.length) {
                    console.log(res.data.data);
                    // positionData.value = res.data.data
                    res.data.data.forEach((item) => {
                        if (item.longitude != null && item.latitude != null) {
                            positionData.value.push({
                                name: item.id,
                                id: item.id,
                                longitude: Number(item.longitude),
                                latitude: Number(item.latitude),
                            });
                        }
                    });
                    if (window.toggle == 2) {
                        putIcons(positionData.value, videoImg);
                        console.log(window.viewer.entities);
                        // const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.canvas);
                        window.viewer.screenSpaceEventHandler.setInputAction(
                            function (e) {
                                var pick = viewer.scene.pick(e.position);
                                if (pick && pick.id) {
                                    console.log(pick.id.id);

                                    videoId.value = pick.id.id;
                                    videoDialogShow.value = true;
                                    proxy.$loading.show();
                                    window.viewer.camera.setView({
                                        destination:
                                            Cesium.Cartesian3.fromDegrees(
                                                pick.id._longitude,
                                                pick.id._latitude,
                                                800,
                                            ),
                                    });
                                }
                            },
                            Cesium.ScreenSpaceEventType.LEFT_CLICK,
                        );
                    } else if (window.toggle == 3) {
                        console.log(window.map1, positionData.value);
                        positionData.value.forEach((item) => {
                            let marker = new AMap.Marker({
                                // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                                icon: new AMap.Icon({
                                    size: new AMap.Size(72, 92), // 图标尺寸
                                    image: "/static/poi/video-new.svg", //绝对路径
                                    imageSize: new AMap.Size(72, 92),
                                }),
                                position: [item.longitude, item.latitude],
                                offset: new AMap.Pixel(-36, -92),
                            });
                            marker.on("click", (e) => {
                                videoId.value = item.name;
                                videoDialogShow.value = true;
                                proxy.$loading.show();
                                window.map1.setZoomAndCenter(
                                    22,
                                    e.target._opts.position,
                                );
                                console.log(videoId.value);
                            });
                            marker.setMap(window.map1);
                        });
                        window.map1.setFitView();
                    }
                }
            });
        } else if (type == 3) {
            console.log("ndksdkshdkdshdshh");
            proxy.$bus.emit("uesMonitor", true);
        }
    }
};

const bayonetDialogShow = ref(false);
const videoDialogShow = ref(false);
const carDialogShow = ref(false);
const carDataDialogShow = ref(false);
/**
 * 人员离岗数据定义
 * departureShow  人员离岗弹框控制
 * departureData  弹框展示数据
 *
 * */
const departureShow = ref(false);
const departureData = ref([]);

const timer = ref(null);
import bayonetImg from "/static/poi/bayonet-new.svg";
import videoImg from "/static/poi/video-new.svg";
import CarDetailDialog from "./subgroup/carDetailDialog.vue";
import CarDataDetailDialog from "./subgroup/carDataDetailDialog.vue";
// import url_success from '../../../assets/images/closed/car_success.svg'
// import url_error from '../../../assets/images/closed/car_error.svg'
// import url from "../../../assets/images/closed/car_success.svg";
import DepartureDialog from "./subgroup/departureDialog.vue";
import url_success from "../../../assets/images/closed/car_success.svg";
import url_error from "../../../assets/images/closed/car_error.svg";

const putIcons = (_datas, img, _parent) => {
    console.log("添加视频icon");
    let imgUrl = img;
    console.log(_datas);
    for (let i = 0; i < _datas.length; i++) {
        const data = _datas[i];
        console.log(data);
        // let alive = devices.has(data.device_serno_singe);
        // if (alive) {
        //   imgUrl = this.aliveImg;
        // } else {
        //   imgUrl = this.notAliveImg;
        // }
        // console.log(window.viewer.entities);
        const entity = window.viewer.entities.add({
            name: data.id,
            // 参数顺序：经度、纬度
            id: data.name,
            longitude: Number(data.longitude) - 0.0062,
            latitude: Number(data.latitude) - 0.00085,
            position: Cesium.Cartesian3.fromDegrees(
                Number(data.longitude) - 0.0062,
                Number(data.latitude) - 0.00085,
                10,
            ), // 标签的位置
            //   label: {
            //     text: "我是一个点",
            //     font: "100px HelVetica",
            //     fillColor: Cesium.Color.RED,
            //   },
            // parent: _parent,
            billboard: {
                image: img,
                width: 72,
                height: 92,
                disableDepthTestDistance: Number.POSITIVE_INFINITY,
                pixelOffset: new Cesium.Cartesian2(0, -46),
            },
            propertity: {
                viewCom: "LivePlayer",

                "SIP用户名/设备编号": data.device_serno_singe,
            },
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            scaleByDistance: new Cesium.NearFarScalar(0, 0, 1, 1),
            pixelOffsetScaleByDistance: new Cesium.NearFarScalar(0, 0, 1, 1),
            click: (t) => {
                // if (t.name != "cameraPoint" || !alive) return;
                // this.play(data.device_serno_singe, (res) => {
                //   this.showPop(res, t.position._value);
                // });
                // console.log(t);
            },
            type: "text", // 自定义属性
        });
        window.viewer.zoomTo(entity);
        console.log(999999999999999999999999999999999);
    }
};
const closeDialog = (value) => {
    console.log(value);
    if (value == "bayonet") {
        bayonetDialogShow.value = false;
    } else if (value == "video") {
        videoDialogShow.value = false;
    } else if (value == "car") {
        carDialogShow.value = false;
    } else if (value == "depart") {
        departureShow.value = false;
    } else if (value == "carData") {
        carDataDialogShow.value = false;
    }
    proxy.$loading.hide();
};
var path = [
    [115.107027873843, 37.967476971868],
    [115.108984044575, 37.9676990161805],
    [115.114581935149, 37.9682895127299],
    [115.114875013849, 37.9647435975399],
    [115.120760157275, 37.9654807783805],
    [115.122757109275, 37.9648187005449],
    [115.126154890766, 37.9638912590139],
    [115.127570772583, 37.9589187898429],
    [115.135388661825, 37.9593751355947],
    [115.140649555561, 37.9557639827728],
    [115.140552198161, 37.9511191584419],
    [115.140682599355, 37.9498663851139],
    [115.127644524961, 37.9465171855449],
    [115.127531161592, 37.9469572161843],
    [115.116666868099, 37.9455974855667],
    [115.115731255449, 37.9503398556584],
    [115.110223040793, 37.9505120787165],
    [115.106759744913, 37.9505159984926],
    [115.107027595251, 37.9674533646081],
    [115.107027873843, 37.967476971868]
];
var polyline1 = null;
const initBoundary = () => {
    if (window.map1 != null && window.toggle == 3) {
        console.log("边界");
        polyline1 = new AMap.Polyline({
            path: path,
            strokeColor: "#49D1AF",
            strokeWeight: 3,
            strokeOpacity: 0.9,
            strokeStyle: "dashed",
            zIndex: 50,
            bubble: true,
        });
        console.log(polyline1);
        window.map1.add([polyline1]);
        window.map1.setFitView();
    }
};
const isTrue = (data) => {
    if (data.drivePlan.route != null) {
        if (
            data.speed > data.drivePlan.speedLimit ||
            !eval(data.drivePlan.route)
                .map((e) => dealWith(e[0]))
                .includes(dealWith(data.lng)) ||
            !eval(data.drivePlan.route)
                .map((e) => dealWith(e[1]))
                .includes(dealWith(data.lat))
        ) {
            return false;
        } else return true;
    } else {
        return true;
    }
};
proxy.$bus.on("cameraPersonLink_listIoc", (val) => {
    quitOnePost();
    //   console.log('test ', val);
});

const quitOnePost = () => {
    cameraPersonLink().then((res) => {
        console.log(res.data.data, "离岗人员数据");
        res.data.data.forEach((car) => {
            if (car != null) {
                console.log(car);
                let marker = new AMap.Marker({
                    icon: new AMap.Icon({
                        size: new AMap.Size(42, 62), // 图标尺寸
                        image:
                            car.alarmed == 0
                                ? "/static/poi/people_success.svg"
                                : "/static/poi/people_error.svg", //绝对路径
                        imageSize: new AMap.Size(42, 62),
                    }),
                    position: [car.lng, car.lat],
                    offset: new AMap.Pixel(-21, -62),
                });
                console.log(marker, "我是人员数据");
                marker.on("click", (e) => {
                    console.log(e);
                    console.log(car);
                    departureData.value = car;
                    departureShow.value = true;
                    proxy.$loading.show();
                    // window.map1.setZoomAndCenter(22, e.target._opts.position)
                    // console.log(carId.value);
                });
                marker.setMap(window.map1);
                window.map1.setFitView();
            }
        });
    });
    alarmInfoPersonRecord({
        pageNum: 1,
        pageSize: 999,
        alarmType: "illegalPark",
    }).then((res) => {
        alarmInfoPersonRecord({
            pageNum: 1,
            pageSize: 999,
            alarmType: "crossLine",
        }).then((res1) => {
            let data = res.data.data.records
                .map((a) => {
                    return {
                        ...a,
                        type: 1,
                    };
                })
                .concat(
                    res1.data.data.records.map((a) => {
                        return {
                            ...a,
                            type: 2,
                        };
                    }),
                );
            data.forEach((car) => {
                if (car != null) {
                    let marker = new AMap.Marker({
                        icon: new AMap.Icon({
                            size: new AMap.Size(42, 62), // 图标尺寸
                            image: "/static/poi/car_error.svg", //绝对路径
                            imageSize: new AMap.Size(42, 62),
                        }),
                        position: [Number(car.longitude), Number(car.latitude)],
                        offset: new AMap.Pixel(-21, -62),
                    });
                    marker.on("click", (e) => {
                        carData.value = car;
                        carDataDialogShow.value = true;
                        proxy.$loading.show();
                        window.map1.setZoomAndCenter(
                            22,
                            e.target._opts.position,
                        );
                    });
                    const boxStyle = `
  width: 120px;
  height: 40px;
  border: 2px solid #47EBEB;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
`;
                    const sizeColor = `
                 width: 100%;
                  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #FFFFFF;
    font-size: 14px;
`;
                    marker.on("mouseover", (e) => {
                        window.infoWindowone = new AMap.InfoWindow({
                            isCustom: true,
                            position: [
                                Number(car.longitude),
                                Number(car.latitude),
                            ],
                            offset: new AMap.Pixel(0, -60),
                            content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${car.carPlate}</div>
                       </div>`,
                        });
                        window.infoWindowone.open(window.map1);
                    });
                    marker.on("mouseout", (e) => {
                        window.infoWindowone.close();
                    });
                    marker.setMap(window.map1);
                    // window.map1.setFitView();
                }
            });
        });
    });
};

const initCarMap = () => {
    if (window.map1 != null && window.toggle == 3) {
        carGpsPoint().then((res) => {
            res.data.data.forEach((car) => {
                if (car != null) {
                    let marker = new AMap.Marker({
                        icon: new AMap.Icon({
                            size: new AMap.Size(42, 62), // 图标尺寸
                            image: isTrue(car)
                                ? "/static/poi/car_success.svg"
                                : "/static/poi/car_error.svg", //绝对路径
                            imageSize: new AMap.Size(42, 62),
                        }),
                        position: [car.lng, car.lat],
                        offset: new AMap.Pixel(-21, -62),
                    });
                    marker.on("click", (e) => {
                        carId.value = car.carPlate;
                        carDialogShow.value = true;
                        proxy.$loading.show();
                        window.map1.setZoomAndCenter(
                            22,
                            e.target._opts.position,
                        );
                        console.log(carId.value);
                    });
                    const boxStyle = `
  width: 120px;
  height: 40px;
  border: 2px solid #47EBEB;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
`;
                    const sizeColor = `
                 width: 100%;
                  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #FFFFFF;
    font-size: 14px;
`;
                    marker.on("mouseover", (e) => {
                        window.infoWindowone = new AMap.InfoWindow({
                            isCustom: true,
                            position: [car.lng, car.lat],
                            offset: new AMap.Pixel(0, -60),
                            content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${car.carPlate}</div>
                       </div>`,
                        });
                        window.infoWindowone.open(window.map1);
                    });
                    marker.on("mouseout", (e) => {
                        window.infoWindowone.close();
                    });
                    marker.setMap(window.map1);
                    // window.map1.setFitView();
                }
            });
        });

        window.clearInterval(timer.value);
        timer.value = window.setInterval(() => {
            quitOnePost();
            window.map1.clearMap();
            initBoundary();
            carGpsPoint().then((res) => {
                res.data.data.forEach((car) => {
                    if (car != null) {
                        let marker = new AMap.Marker({
                            icon: new AMap.Icon({
                                size: new AMap.Size(42, 62), // 图标尺寸
                                image: isTrue(car)
                                    ? "/static/poi/car_success.svg"
                                    : "/static/poi/car_error.svg", //绝对路径
                                imageSize: new AMap.Size(42, 62),
                            }),
                            position: [car.lng, car.lat],
                            offset: new AMap.Pixel(-21, -62),
                        });
                        marker.on("click", (e) => {
                            carId.value = car.carPlate;
                            carDialogShow.value = true;
                            proxy.$loading.show();
                            window.map1.setZoomAndCenter(
                                22,
                                e.target._opts.position,
                            );
                            console.log(carId.value);
                        });
                        const boxStyle = `
  width: 120px;
  height: 40px;
  border: 2px solid #47EBEB;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
`;
                        const sizeColor = `
                 width: 100%;
                  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #FFFFFF;
    font-size: 14px;
`;
                        marker.on("mouseover", (e) => {
                            window.infoWindowone = new AMap.InfoWindow({
                                isCustom: true,
                                position: [car.lng, car.lat],
                                offset: new AMap.Pixel(0, -60),
                                content: `<div style="${boxStyle}">
                            <div style="${sizeColor}">${car.carPlate}</div>
                       </div>`,
                            });
                            window.infoWindowone.open(window.map1);
                        });
                        marker.on("mouseout", (e) => {
                            window.infoWindowone.close();
                        });
                        marker.setMap(window.map1);
                    }
                });
                //   window.map1.setFitView();
            });
        }, 60000);
    }
};
onMounted(() => {
    showFlag.value = window.toggle;
    if (window.heatmap != null) {
        window.heatmap.setMap(null);
    }
    initBoundary();
    // initCarMap();
    // quitOnePost();
    proxy.$bus.on("change_toggle", (val) => {
        console.log("change_toggle ", val);
        showFlag.value = val;
        active.value = 0;
    });

    proxy.$bus.on("clicJump1", (val) => {
        if (val.alarmObjectId != null && val.alarmObjectId != undefined) {
            console.log(val);
            console.log(val.alarmTime.split(" ")[0]);
            if (val.tag == "人员离岗告警") {
                //         console.log("人员离岗告警", val.alarmObjectId);
                //         var car =null
                //         detail({ id: val.alarmObjectId }).then((res) => {
                //             console.log('测试',res.data.data);
                //             car = res.data.data
                //             console.log(res.data);
                //             let marker = new AMap.Marker({
                //   icon: new AMap.Icon({
                //     size: new AMap.Size(42, 62), // 图标尺寸
                //     image: '/static/poi/people_error.svg',//绝对路径
                //     imageSize: new AMap.Size(42, 62)
                //   }),
                //   position: [Number(val.longitude), Number(val.latitude)],
                //   offset: new AMap.Pixel(-21, -62)
                // });
                // console.log(marker, '我是人员数据')
                // marker.on("click", (e) => {
                //   console.log(e)
                //   console.log(car)
                //   departureData.value = car;
                //   departureShow.value = true;proxy.$loading.show()
                //   // window.map1.setZoomAndCenter(22, e.target._opts.position)
                //   // console.log(carId.value);
                // });
                // marker.setMap(window.map1);
                // window.map1.setFitView();
                //         })
                return;
            } else {
                if (
                    val.acquisitionValue != undefined &&
                    val.acquisitionValue != null &&
                    val.alarmTime != undefined &&
                    val.alarmTime != null
                ) {
                    value2.value = val.alarmTime.split(" ")[0];
                    changeDate(value2.value);
                    carPlate.value = val.acquisitionValue;
                    changeEnterpriseName(carPlate.value);
                }
            }
        }
    });
});

onUnmounted(() => {
    window.clearInterval(timer.value);
    timer.value = null;
    proxy.$bus.off("change_toggle");
});
</script>

<style lang="less" scoped>
.input-wrapper {
    width: 300px;
    height: 40px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    left: 496px;

    :deep(.el-input__wrapper) {
        background-color: transparent;
        box-shadow: 0 0 0 0;
        border-radius: 4px;
        border: 1px solid #30abe8;
        height: 22px;
        padding: 7px 12px;
        width: 180px;
        margin-bottom: 10px;
        //margin-bottom: 10px;
    }
    :deep(.el-input__wrapper .is_focus) {
        background-color: transparent;
        box-shadow: 0 0 0 0;
        border-radius: 4px;
        border: 1px solid #30abe8;
        height: 22px;
        padding: 7px 12px;
        width: 180px;
        margin-bottom: 10px;
        //margin-bottom: 10px;
    }
    :deep(.el-date-editor) {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 40px; /* 157.143% */
        margin-bottom: 10px;
        width: 180px;
    }

    :deep(.el-input__inner) {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 40px; /* 157.143% */
        width: 180px;
    }

    :deep(.el-range-input) {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
    }

    :deep(.el-input__inner::placeholder) {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 40px; /* 157.143% */
    }
}

.closed-management-navigation {
    width: 130px;
    height: 92px;
    z-index: 1000;
    position: absolute;
    top: 690px;
    left: 496px;

    > div:not(:first-child) {
        margin-top: 24px;
    }

    .btn {
        width: 156px;
        height: 34px;
        background: url("../../../assets/images/jump-btn/nav-big.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
    }

    .active-btn {
        width: 156px;
        height: 34px;
        background: url("../../../assets/images/jump-btn/nav-active-big.svg")
            no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        // gap: 24px;
    }

    .icon {
        width: 24px;
        height: 24px;
        // margin: auto 0;
        margin-top: 4px;
        margin-left: 12px;
    }

    .text {
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        margin: auto 0;
        margin-left: 24px;
    }
}
</style>
