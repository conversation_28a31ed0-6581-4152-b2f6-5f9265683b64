<template>
    <div class="dialog-all-wrapper">
        <div class="enterprise-information-dialog-wrapper">
            <div class="enterprise-information-dialog border">
                <div class="dialog-header">
                    <div class="header-title">详情</div>
                    <img src="@/assets/newImages/Dialog/dialog-close.svg" class="header-close" @click="closeDialog">
                </div>
                <div class="dialog-content">
                    <div class="top-info">
                        <div class="info-item">
                            <div class="info-item-title">企业名称：</div>
                            <div class="info-item-value">{{basicObj.companyName}}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-item-title">行业类别：</div>
                            <div class="info-item-value">{{basicObj.industryType}}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-item-title">作业名称：</div>
                            <div class="info-item-value">{{basicObj.workName}}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-item-title">作业类别：</div>
                            <div class="info-item-value">{{basicObj.workType}}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-item-title">作业内容：</div>
                            <div class="info-item-value">{{basicObj.workContent}}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-item-title">作业时间：</div>
                            <div class="info-item-value">{{basicObj.beginTime&&basicObj.endTime?basicObj.beginTime+'~'+basicObj.endTime:'-'}}</div>
                        </div>
                    </div>
                    <div class="top-selects">
                        <div class="slect-tabs" ref="tabsRef">
                            <div class="tab-item" v-for="(item,index) in topTabs" :key="index" @click="clickTab(item)">
                                <div class="tab-item-text" :class="{'active-tab-item':currentTab === item}" >{{ item }}</div>
                                <div class="tab-item-line" v-if="currentTab === item"></div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-content">
                        <!-- 内容部分 -->
                        <check-result :checkObj="detailObj" v-if="currentTab=='抽查结果'"></check-result>
                        <basic-information :dataObj="basicObj" v-if="currentTab=='基础信息'"></basic-information>
                        <risk-information :dataObj="basicObj" v-if="currentTab=='风险信息'"></risk-information>
                        <camera :dataObj="basicObj" v-if="currentTab=='摄像头'"></camera>
                        <gas-equipment :dataObj="basicObj" v-if="currentTab=='气体设备'"></gas-equipment>
                    </div>
                </div>
            </div>
            <div class="corner-bottom-left"></div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed, defineEmits, toRefs } from "vue";
import { check_detail } from "@/assets/js/api/dialog/specialAssignments";
//基本信息
import basicInformation from './basicInformation.vue'
//抽查结果
import checkResult from './checkResult.vue';
//风险信息
import riskInformation from './riskInformation.vue';
//摄像头
import camera from './camera.vue';
//气体设备
import gasEquipment from './gasEquipment.vue';
const emit = defineEmits(["closeDialog"]);
const props = defineProps({
    sonId: {
        type: [String, Number],
    },
});
const { sonId } = toRefs(props);
//企业基础信息
// import basic from "./basic.vue"


//
// import patentFile from "./patentFile.vue"
//顶部tab
let topTabs = ref(['抽查结果','基础信息','风险信息','摄像头','气体设备']);
let currentTab = ref('抽查结果');
const tabsRef = ref(null);


const clickTab = (item) => {
    currentTab.value = item;
};
//基础信息
let basicObj = ref({
companyName:'',
industryType:'',
workName:'',
workType:'',
workContent:'',
beginTime:'',
endTime:'',
})
//详情数据
let detailObj = ref({})
//获取详情数据
const getData = () =>{
    check_detail(sonId.value).then((res) => {
        console.log(res);
        if(res.data&&res.data.data){
            detailObj.value = res.data.data;
            console.log(detailObj.value,'详情数据');
            
            if(res.data.data.specialWorkVo){
                basicObj.value = res.data.data.specialWorkVo;
            }
        }
    })
}
onMounted(() => {
    console.log(sonId.value,'详情---------');
    if(sonId.value){
    getData()

    }
    
});

const closeDialog = () => {
    emit('closeDialog');
};
</script>

<style lang="less" scoped>
.tab-arrow {
    width: 16px;
    height: 16px;
    cursor: pointer;
    flex-shrink: 0;
    
    &:hover {
        opacity: 0.8;
    }
    
    &:active {
        opacity: 0.6;
    }
}

.dialog-all-wrapper {
    width: 100vw;
    height: 100vh;
    z-index: 1003;
    // background-color:antiquewhite;
    background-color: rgba(0, 0, 0, 0.8);

    position: absolute;
    top: 0px;
    left: 0px;
    .enterprise-information-dialog-wrapper {
        width: 800px;
        height: 880px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        // 添加底部两个装饰三角形
        &::after,
        .corner-bottom-left {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 6px;
            border-color: rgba(26, 159, 255, 0.6) transparent transparent
                transparent;
            z-index: 1;
        }

        // 左下角装饰三角形
        .corner-bottom-left {
            bottom: -7.5px;
            left: -6px;
            transform: rotate(45deg);
        }

        // 右下角装饰三角形
        &::after {
            bottom: -7.5px;
            right: -7.5px;
            transform: rotate(-45deg);
        }
    }
    .enterprise-information-dialog {
        width: 800px;
        height: 880px;

        background-color: rgba(5, 29, 46, 0.6);

        clip-path: polygon(
            10px 0,/* 左上角开始 */ 
            calc(100% - 10px) 0, /* 右上角左侧 */ 
            100% 10px,/* 右上角下侧 */
            100% calc(100% - 10px),/* 右下角上侧 */ 
            calc(100% - 10px) 100%, /* 右下角左侧 */ 
            10px 100%,/* 左下角右侧 */ 
            0 calc(100% - 10px),/* 左下角上侧 */ 
            0 10px /* 左上角下侧 */
        );
    }
    .dialog-header {
        width: 768px;
        height: 56px;
        padding: 0 16px;
        margin-left: -1px;
        background: url("@/assets/newImages/Dialog/dialog-header.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-title{
            font-family: MStiffHei PRC;
font-weight: 400;
font-size: 18px;
line-height: 26px;
letter-spacing: 0%;
background: 
        linear-gradient(180deg, rgba(26, 159, 255, 0.45) 20%, rgba(26, 159, 255, 0) 80%),
        linear-gradient(180deg, #FFFFFF 15.63%, #2AD9FF 87.5%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
        }
        .header-close{
            width: 16px;
height: 16px;

        }
    }
    .border {
        border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
        /* 使用渐变作为边框图像 */
        border-image-source: linear-gradient(
            360deg,
            rgba(26, 159, 255, 0.6) 0%,
            rgba(26, 159, 255, 0) 100%
        );
        border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
        border-image-width: 1px;
        border-top: none;
    }
    // 弹窗内部
    .dialog-content{
        width: 760px;
        height: 840px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap:14px;
        .top-info{
            width: 656px;
height: 120px;
display: flex;
flex-wrap: wrap;

.info-item{
    width: 328px;
    height: 40px;
    display: flex;
    font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
.info-item-title{
    width: 84px;
    color: rgba(128, 234, 255, 1);

}
.info-item-value{
    color: rgba(255, 255, 255, 1);

}

}
        }
    
        .top-selects{
            width: 760px;
            height: 48px;
            border-bottom: 1px solid rgba(128, 234, 255, 0.3);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .slect-tabs {
            width: 760px;
            height: 48px;
            display: flex;
            align-items: center;
            gap: 20px;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            
            /* 隐藏滚动条 - webkit浏览器 */
            &::-webkit-scrollbar {
                display: none;
            }
            
            /* 隐藏滚动条 - Firefox */
            scrollbar-width: none;
            
            /* 隐藏滚动条 - IE */
            -ms-overflow-style: none;
            
            .tab-item {
                height: 36px;
                padding-top: 12px;
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;
                text-align: center;
                color: rgba(128, 234, 255, 1);
                white-space: nowrap; /* 防止文字换行 */
                flex-shrink: 0; /* 防止项目被压缩 */
            }
            .tab-item-line {
                width: 24px;
                height: 2px;
                background-color: rgba(255, 198, 26, 1);
                margin: auto;
                margin-top: 10px;
            }
            .active-tab-item {
                color: rgba(255, 198, 26, 1);
            }
        }
        .tab-content {
            height: 584px;
            // background-color: #2AD9FF;
        }

    }
    
}
</style>
