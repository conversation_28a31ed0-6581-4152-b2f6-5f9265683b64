<svg width="48" height="60" viewBox="0 0 48 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="24" cy="24" r="24" fill="#05202E" fill-opacity="0.6"/>
<circle cx="24" cy="24" r="24" fill="url(#paint0_radial_2981_24050)"/>
<g clip-path="url(#clip0_2981_24050)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M21 18L30 16L34 24V30H32C32 31.1046 31.1046 32 30 32C28.8954 32 28 31.1046 28 30H21H20C20 31.1046 19.1046 32 18 32C16.8954 32 16 31.1046 16 30H14V22L17 19H21V18ZM17.5 21H19V24H16V22.5L17.5 21Z" fill="white"/>
</g>
<path d="M24 60L17 50L31 50L24 60Z" fill="url(#paint1_linear_2981_24050)"/>
<defs>
<radialGradient id="paint0_radial_2981_24050" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(24 24) rotate(90) scale(24)">
<stop offset="0.165778" stop-color="#30ABE8" stop-opacity="0"/>
<stop offset="1" stop-color="#30ABE8"/>
</radialGradient>
<linearGradient id="paint1_linear_2981_24050" x1="24" y1="60" x2="24" y2="50" gradientUnits="userSpaceOnUse">
<stop stop-color="#00BFFF"/>
<stop offset="1" stop-color="#0099CC" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_2981_24050">
<rect width="24" height="24" fill="white" transform="translate(12 12)"/>
</clipPath>
</defs>
</svg>
