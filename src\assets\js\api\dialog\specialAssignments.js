import { fetch } from "../../request";
//特殊作业-分页列表
export const select_page = (params) =>fetch("/safety-v2/special-work/select-page", { ...params }, "GET");
//特殊作业详情
export const special_work_detail = (params) =>fetch(`/safety-v2/special-work/select-detail/${params}`, { ...params }, "GET");
//特殊作业类型列表
export const specialType_list_enum = (params) =>fetch("/safety-v2/number-type/specialType-list", { ...params }, "GET");
//抽查弹窗列表
export const special_work_check = (params) =>fetch("/safety-v2/special-work-check/select-page", { ...params }, "GET");
//抽查详情
export const check_detail = (params) =>fetch(`/safety-v2/special-work-check/select-detail/${params}`, { ...params }, "GET");
//获取视频流
export const camera_flv = (params) =>fetch("/safety-v2/remote/cameraFlv", { ...params }, "POST");
//大气查询
export const getAtmospherePageList = (params) =>fetch("/environment/environment-testing-station-historical/getAtmospherePageList", { ...params }, "GET");