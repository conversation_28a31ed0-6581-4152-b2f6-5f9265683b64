<template>
    <div class="table">
                <table>
                    <tr>
                        <td class="header-cell">
                            企业名称
                        </td>
                        <td class="header-cell">
                            统一社会信用代码
                        </td>
                        <td class="header-cell">
                            注册资金（万元）
                        </td>
                        <td class="header-cell">
                            成立日期
                        </td>
                    </tr>
                    <tr>
                        <td class="data-cell">
                            <div
                                class="cell-inner"
                                :title="dialogData.enterpriseName"
                            >
                                {{ dialogData.enterpriseName }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div
                                class="cell-inner"
                                :title="dialogData.creditCode"
                            >
                                {{ dialogData.creditCode }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.legalPersonName">
                                {{ dialogData.legalPersonName }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.establishDate">
                                 {{ dialogData.establishDate }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="header-cell">
                            经营状态
                        </td>
                        <td class="header-cell">
                            法定代表人
                        </td>
                        <td class="header-cell">
                            行业
                        </td>
                        <td class="header-cell">
                            规模情况
                        </td>
                        
                    </tr>
                    <tr>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.operateStatus">
                                {{ dictListManage&&dictListManage.find(i=>i.dictEncoding==dialogData.operateStatus)!=undefined?dictListManage.find(i=>i.dictEncoding==dialogData.operateStatus).dictName:'-' }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.legalPersonName">
                                {{ dialogData.legalPersonName }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.industry">
                                {{ dialogData.industry }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.scale">
                                {{ dialogData.scale }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="header-cell">
                            从业人员数据
                        </td>
                        <td class="header-cell">
                            母公司名称
                        </td>
                        <td class="header-cell">
                            集团公司名称
                        </td>
                        <td class="header-cell">
                            是否为国有企业
                        </td>
                    </tr>
                    <tr>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.onjobStaff">
                                {{ dialogData.onjobStaff }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.parentCompany">
                                {{ dialogData.parentCompany }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.groupCompanyName">
                                {{ dialogData.groupCompanyName }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.isStateOwned">
                                {{ dialogData.isStateOwned?'是':'否' }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="header-cell">
                            隶属关系
                        </td>
                        <td class="header-cell">
                            联系方式
                        </td>
                        <td class="header-cell">
                            安全生产情况
                        </td>
                        <td class="header-cell">
                            生态环境情况
                        </td>
                    </tr>
                    <tr>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.memberShip">
                                {{ dictListMember&&dictListMember.find(i=>i.dictEncoding==dialogData.memberShip)!=undefined?dictListMember.find(i=>i.dictEncoding==dialogData.memberShip).dictName:'-' }}
                            </div>
                        </td>
                        <td class="data-cell">
                            <div class="cell-inner" :title="dialogData.contactNumber">
                                {{ dialogData.contactNumber }}
                            </div>
                        </td>
                        <td class="data-cell">
                             <div class="cell-inner" :title="dialogData.safetyProduction">
                                {{dialogData.safetyProduction}}
                            </div>
                        </td>
                        <td class="data-cell">
                             <div class="cell-inner" :title="dialogData.ecologicalEnvironment">
                                {{ dialogData.ecologicalEnvironment }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="header-cell" colspan="4">
                            经营范围
                        </td>
                    </tr>
                    <tr>
                        <td class="data-cell-big" colspan="4">
                            <div class="cell-inner-big" :title="dialogData.businessScope">
                                {{ dialogData.businessScope }}
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
</template>

<script setup>
import { enterprise_info_detail,dict_allList } from "@/assets/js/api/dialog/secureBase";
import { ref, reactive, onMounted, onBeforeUnmount, computed, defineEmits,getCurrentInstance,toRefs } from "vue";
const props = defineProps({
    sonId: {
        type: [String, Number],
    },
});
const { sonId } = toRefs(props);
const { proxy } = getCurrentInstance();
let dialogData = ref({
    parkName: "",
    rank: "",
    legalPersonName: "",
    manageDepart:"",
    operateStatus: "",
    legalPersonName: "",
    industryPosition: "",
    registerNum:"",
    onjobStaff: "",
    parentCompany: "",
    groupCompanyName: "",
    businessScope:"",
    contactNumber:""
});
//经营状态字典
let dictListManage = ref([]);
//行业字典
let dictListIndustry = ref([]);
//隶属关系字典
let dictListMember = ref([]);
const getDict = () => {
    dict_allList({
        pid: 278,
    }).then((res) => {
        if (res.data && res.data.data) {
            dictListManage.value = res.data.data;
        }
    });
    dict_allList({
        pid: 273,
    }).then((res) => {
        if (res.data && res.data.data) {
            dictListMember.value = res.data.data;
        }
    });
    
};

const getData = () =>{
    enterprise_info_detail(sonId.value).then((res)=>{
        
        if(res.data&&res.data.data){
            dialogData.value = res.data.data;
            
        }
    })
}
getDict();
onMounted(() => {
   getData()
});
onBeforeUnmount(() => {
});
</script>

<style lang="less" scoped>
.table {
        width: 1120px;
    height: 600px;
        // background-color: antiquewhite;
        table {
            border-collapse: collapse;
        }
        table td {
            border: 1px solid rgba(25, 159, 255, 1);
        }

        .header-cell {
            width: 248px;
height: 40px;
padding-right: 16px;
padding-left: 16px;

            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
text-align: left;
color: rgba(128, 234, 255, 1);
            background-color: rgba(25, 159, 255, 0.1);
        }
        .data-cell {
            width: 248px;
height: 40px;
padding-right: 16px;
padding-left: 16px;

            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
text-align: left;
color: rgba(255, 255, 255, 1);
            .cell-inner {
                width: 248px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
        .data-cell-big {
            width: 1088px;
            max-height: 160px;
            padding: 16px;

            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 32px;
            text-align: left;
            color: rgba(255, 255, 255, 1);
            .cell-inner-big {
                width: 1088px;
                overflow: wrap;
            }
        }
    }
</style>
  