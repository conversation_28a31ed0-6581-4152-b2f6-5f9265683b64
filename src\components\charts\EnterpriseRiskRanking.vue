<template>
  <div class="enterprise-risk-ranking" ref="chartContainer">
    <div id="riskRankingChart" :style="{ width: '100%', height: '100%' }"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';

const chartContainer = ref(null);
let chart = null;

// 企业数据
const enterpriseData = [
  { name: '企业名称1', value: 1234, color: ['#4CAF50', '#1B5E20'], bgColor: 'rgba(77, 203, 98, 0.3)' }, // 绿色
  { name: '企业名称1', value: 1234, color: ['#2196F3', '#0D47A1'], bgColor: 'rgba(48, 171, 232, 0.3)' }, // 蓝色
  { name: '企业名称1', value: 1234, color: ['#9C27B0', '#4A148C'], bgColor: 'rgba(153, 178, 255, 0.3)' }, // 紫色
  { name: '企业名称1', value: 1234, color: ['#FFC107', '#FF6F00'], bgColor: 'rgba(255, 198, 26, 0.3)' }, // 黄色
  { name: '企业名称1', value: 1234, color: ['#FF5722', '#BF360C'], bgColor: 'rgba(255, 121, 26, 0.3)' }, // 橙色
];

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  // 创建图表实例
  chart = echarts.init(document.getElementById('riskRankingChart'));

  // 计算最大值用于百分比显示
  const maxValue = Math.max(...enterpriseData.map(item => item.value));

  // 计算四舍五入到最接近的分段位置的值
  const calculateRoundedValue = (value) => {
    // 计算每个分段的值（总共30个分段）
    const segmentSize = maxValue / 30;
    // 计算最接近的分段数
    const segments = Math.round(value / segmentSize);
    // 返回对应的值（确保刚好落在分隔线上）
    return segments * segmentSize;
  };

  // 准备数据
  const seriesData = enterpriseData.map((item) => {
    return {
      name: item.name,
      // 使用四舍五入的值确保分隔线对齐
      value: calculateRoundedValue(item.value),
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: item.color[0] },
          { offset: 0.7, color: item.color[0] },
          { offset: 1, color: item.color[1] }
        ])
      }
    };
  });

  // 图表配置
  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '80px',
      right: '80px',
      top: '10px',
      bottom: '10px',
      containLabel: true
    },
    xAxis: {
      show: false,
      type: 'value',
      max: maxValue
    },
    yAxis: {
      type: 'category',
      inverse: true,
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { show: false },
      data: enterpriseData.map(item => item.name)
    },
    series: [
      // 未占比部分（暗色背景）
      {
        name: '未占比部分',
        type: 'bar',
        barWidth: 12,
        barGap: '-100%', // 确保与主条重叠
        data: enterpriseData.map((item) => ({
          value: maxValue,
          itemStyle: {
            color: item.bgColor // 使用对应的半透明背景色
          }
        })),
        z: 1,
        // 暗色部分的分隔线 - 使用固定间隔
        markLine: {
          silent: true,
          symbol: 'none',
          lineStyle: {
            type: 'solid',
            width: 1, // 减小宽度为1px
            color: 'rgba(0, 0, 0, 0.3)', // 降低透明度使分隔线不那么突出
          },
          data: Array.from({ length: 29 }, (_, i) => {
            // 使用固定间隔，而不是基于数值计算
            const pos = maxValue * ((i + 1) / 30);
            return [{
              xAxis: pos,
              yAxis: -0.5,
            }, {
              xAxis: pos,
              yAxis: 4.5,
            }];
          })
        }
      },
      // 占比部分（亮色渐变）
      {
        name: '风险数量',
        type: 'bar',
        barWidth: 12,
        data: seriesData,
        label: {
          show: true,
          position: 'right',
          // 使用原始值而不是四舍五入后的值
          formatter: function(params) {
            // 找到原始数据中对应的项
            const originalItem = enterpriseData.find(item => item.name === params.name);
            return originalItem ? originalItem.value + '次' : params.value + '次';
          },
          fontSize: 16,
          color: '#ffffff',
          fontWeight: 'bold'
        },
        z: 2,
        // 亮色部分的分隔线 - 使用固定间隔
        markLine: {
          silent: true,
          symbol: 'none',
          lineStyle: {
            type: 'solid',
            width: 1, // 减小宽度为1px
            color: 'rgba(0, 0, 0, 0.3)', // 降低透明度使分隔线不那么突出
          },
          data: Array.from({ length: 29 }, (_, i) => {
            // 使用固定间隔，与暗色部分保持一致
            const pos = maxValue * ((i + 1) / 30);
            return [{
              xAxis: pos,
              yAxis: -0.5,
            }, {
              xAxis: pos,
              yAxis: 4.5,
            }];
          })
        }
      },
      {
        name: '企业名称',
        type: 'custom',
        renderItem: (params, api) => {
          const index = params.dataIndex;
          const name = enterpriseData[index].name;
          // value 变量未使用，移除
          const coord = api.coord([0, index]);

          // 序号背景
          const numberBg = {
            type: 'rect',
            shape: {
              x: coord[0] - 60,
              y: coord[1] - 15,
              width: 40,
              height: 30
            },
            style: {
              fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: enterpriseData[index].color[0] },
                { offset: 1, color: enterpriseData[index].color[1] }
              ]),
              borderRadius: [5, 0, 0, 5]
            }
          };

          // 序号文字
          const numberText = {
            type: 'text',
            style: {
              text: `0${index + 1}`,
              x: coord[0] - 40,
              y: coord[1],
              fill: '#ffffff',
              font: 'bold 16px Arial',
              textAlign: 'center',
              textVerticalAlign: 'middle'
            }
          };

          // 企业名称
          const nameText = {
            type: 'text',
            style: {
              text: name,
              x: coord[0] - 10,
              y: coord[1],
              fill: '#ffffff',
              font: '16px Arial',
              textAlign: 'left',
              textVerticalAlign: 'middle'
            }
          };

          return {
            type: 'group',
            children: [numberBg, numberText, nameText]
          };
        },
        data: enterpriseData.map(item => item.value),
        z: 1
      }
    ]
  };

  // 设置图表配置
  chart.setOption(option);

  // 响应窗口大小变化
  window.addEventListener('resize', handleResize);
};

// 处理窗口大小变化
const handleResize = () => {
  chart && chart.resize();
};

// 组件挂载时初始化图表
onMounted(() => {
  initChart();
});

// 组件卸载前清理资源
onBeforeUnmount(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.enterprise-risk-ranking {
  width: 100%;
  height: 100%;
  min-height: 300px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 10px;
}
</style>
