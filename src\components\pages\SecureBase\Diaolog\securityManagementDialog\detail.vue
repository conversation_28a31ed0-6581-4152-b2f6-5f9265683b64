<template>
    <div class="dialog-all-wrapper">
        <div class="security-managemen-detail-wrapper">
            <div class="security-managemen-detail border">
                <div class="dialog-header">
                    <div class="header-title">安全管理机构</div>
                    <img
                        src="@/assets/newImages/Dialog/dialog-close.svg"
                        class="header-close"
                        @click="closeDialog"
                    />
                </div>
                <div class="dialog-content">
                    <div class="top-content">
                        <div class="top-top">{{sonObj.enterpriseName}}</div>
                        <div class="top-line"></div>
                        <div class="top-bottom">
                            <div class="top-bottom-item">
                                <div class="title">部门名称：</div>
                                <div class="value">{{sonObj.departName}}</div>
                            </div>
                             <div class="top-bottom-item">
                                <div class="title">部门电话：</div>
                                <div class="value">{{sonObj.departPhone}}</div>
                            </div>
                             <div class="top-bottom-item">
                                <div class="title">办公地点：</div>
                                <div class="value">{{sonObj.workPlace}}</div>
                            </div>
                             <div class="top-bottom-item">
                                <div class="title">计划编制人数：</div>
                                <div class="value">{{sonObj.planRegularNum}}</div>
                            </div>
                             <div class="top-bottom-item">
                                <div class="title">实际在编人数：</div>
                                <div class="value">{{sonObj.actualRegularNum}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="bottom">
                    <el-table
                        class="tablebox"
                        :data="tableData"
                        style="width: 100%"
                    >
                        <el-table-column
                            prop="jobName"
                            label="岗位名称"
                            show-overflow-tooltip
                        />
                        <el-table-column
                            prop="personName"
                            label="人员姓名"
                            show-overflow-tooltip
                        />
                        <el-table-column
                            prop="isFull"
                            label="是否专职"
                            show-overflow-tooltip
                        />
                        <el-table-column
                            prop="productDuty"
                            label="安全生产职责"
                            show-overflow-tooltip
                        />
                        
                    </el-table>
                    <el-pagination
                        class="pagination"
                        background
                        v-model:currentPage="pageNum"
                        :page-size="pageSize"
                        @current-change="handleCurrentChange"
                        layout="->,total, prev, pager, next"
                        :total="total"
                    />
                </div>
                </div>
            </div>
            <div class="corner-bottom-left"></div>
        </div>
    </div>
</template>

<script setup>
import {
    ref,
    reactive, 
    onMounted,
    onBeforeUnmount,
    computed,
    defineEmits,
    toRefs
} from "vue";
import { emergency_institution_person_page } from "@/assets/js/api/dialog/secureBase";

const emit = defineEmits(["closeDialog"]);
const props = defineProps({
    sonObj: {
        type: [Number, String],
    },
});
const { sonObj } = toRefs(props);

//查询条件
let queryParams = ref({});

const tableData = ref([
    {
        name:1
    }
]);
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);
//字典
const dictList = ref([]);

const getDict = () =>{
dict_allList({
    pid:115
}).then((res)=>{
    if(res.data&&res.data.data){
        dictList.value = res.data.data;
    }
})
}
const handleCurrentChange = (val) => {
    pageNum.value = val;
    getData()
};
const getData = () =>{
    emergency_institution_person_page({
        id:sonObj.id,
        pageNum:pageNum.value,
        pageSize:pageSize.value
    }).then((res)=>{
        console.log(res);
        if(res.data&&res.data.data){
            tableData.value=res.data.data.list;
            total.value=res.data.data.total
        }
    })
}
// //打开详情弹窗
// const toDetile = (val) =>{

// } 

// getDict()

onMounted(() => {
    console.log(sonObj.value,'sonObj.value');
    
    getData()
});
onMounted(() => {});
const closeDialog = () => {
    emit("closeDialog", "detailShow");
};
</script>

<style lang="less" scoped>
.dialog-all-wrapper {
    width: 100vw;
    height: 100vh;
    z-index: 1005;
    // background-color:antiquewhite;
    background-color: rgba(0, 0, 0, 0.5);

    position: absolute;
    top: 0px;
    left: 0px;
    .security-managemen-detail-wrapper {
        width: 1160px;
        height: 720px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        // 添加底部两个装饰三角形
        &::after,
        .corner-bottom-left {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 6px;
            border-color: rgba(26, 159, 255, 0.6) transparent transparent
                transparent;
            z-index: 1;
        }

        // 左下角装饰三角形
        .corner-bottom-left {
            bottom: -7.5px;
            left: -6px;
            transform: rotate(45deg);
        }

        // 右下角装饰三角形
        &::after {
            bottom: -7.5px;
            right: -7.5px;
            transform: rotate(-45deg);
        }
    }
    .security-managemen-detail {
        width: 1160px;
        height: 720px;

        background-color: rgba(5, 29, 46, 0.6);

        clip-path: polygon(
            10px 0,
            /* 左上角开始 */ calc(100% - 10px) 0,
            /* 右上角左侧 */ 100% 10px,
            /* 右上角下侧 */ 100% calc(100% - 10px),
            /* 右下角上侧 */ calc(100% - 10px) 100%,
            /* 右下角左侧 */ 10px 100%,
            /* 左下角右侧 */ 0 calc(100% - 10px),
            /* 左下角上侧 */ 0 10px /* 左上角下侧 */
        );
    }
    .dialog-header {
        width: 1128px;
        height: 56px;
        padding: 0 16px;
        margin-left: -1px;
        background: url("@/assets/newImages/Dialog/dialog-header.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-title {
            font-family: MStiffHei PRC;
            font-weight: 400;
            font-size: 18px;
            line-height: 26px;
            letter-spacing: 0%;
            background: linear-gradient(
                    180deg,
                    rgba(26, 159, 255, 0.45) 20%,
                    rgba(26, 159, 255, 0) 80%
                ),
                linear-gradient(180deg, #ffffff 15.63%, #2ad9ff 87.5%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .header-close {
            width: 16px;
            height: 16px;
        }
    }
    .border {
        border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
        /* 使用渐变作为边框图像 */
        border-image-source: linear-gradient(
            360deg,
            rgba(26, 159, 255, 0.6) 0%,
            rgba(26, 159, 255, 0) 100%
        );
        border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
        border-image-width: 1px;
        border-top: none;
    }
    // 弹窗内部
    .dialog-content {
        width: 1120px;
        height: 624px;
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap:16px;
        .top-content {
            width: 1120px;
            height: 168px;
            // background-color: antiquewhite;
            .top-top{
                // width: 160px;
height: 40px;
font-family: Noto Sans SC;
font-weight: 400;
font-size: 16px;
line-height: 24px;
color: rgba(255, 255, 255, 1);


            }
            .top-line{
                width: 1120px;
// height: 1px;
border: 1px dashed rgba(128, 234, 255, 1);
margin: 12px 0;
            }
            .top-bottom{
                width: 1120px;
                height: 80px;
                display: flex;
                // gap: 32px;
                flex-wrap: wrap;
                .top-bottom-item{
                    width: 362px;
                    height: 40px;
                    display: flex;
                    .title{
                        font-family: Noto Sans SC;
font-weight: 400;
font-size: 16px;
line-height: 40px;
text-align: left;
color: rgba(128, 234, 255, 1);

                    }
                    .value{
                        font-family: Noto Sans SC;
font-weight: 400;
font-size: 16px;
line-height: 40px;
text-align: left;
color: rgba(255, 255, 255, 1);
                    }
                }
            }
        }
        .bottom {
        width: 1120px;
        height: 504px;
        // background-color: #30abe8;
        .tablebox {
            //表格四个边框的颜色
            // border: 1px solid #30abe8 !important;
            border:none !important;
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;

            th.el-table__cell {
                border: none !important;
            }
        }

        :deep(.el-table),
        :deep(.el-table__expanded-cell) {
            background-color: transparent !important;
            color: #ffffff;
        }

        // 设置表头颜色
        :deep(.el-table th) {
            // border-bottom: 1px solid #30abe8 !important;
            height: 40px;
            background-color: rgba(25, 159, 255, 0.1) !important;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color: rgba(128, 234, 255, 1);
        }

        .el-table__header-wrapper,
        .el-table__fixed-header-wrapper {
            //表头的样式
            th {
                border-bottom: none !important;
            }
        }

        /* 表格内背景颜色 */

        :deep(.el-table) tr,
        :deep(.el-table) td {
            background-color: transparent !important;
        }

        :deep(.el-table) tr td {
            background-color: transparent !important;
            height: 58px;
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            /* 157.143% */
            letter-spacing: -0.28px;
            // border-color: #30ABE8 !important;
        }
          // 表头样式（去掉底部边框）
        :deep(.el-table__header) {
            .el-table__cell {
            border-bottom: none !important; 
            background-color: rgba(25, 159, 255, 0.1) !important;
            color: rgba(128, 234, 255, 1);
            }
        }

        // 表格内容样式（保留底部边框）
        :deep(.el-table__body) {
            .el-table__cell {
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;
            height: 40px;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color:rgba(255, 255, 255, 1)
            }
        }

        /* 去掉table 最底部的线 */
        :deep(.el-table__inner-wrapper::before) {
            left: 0;
            bottom: 0;
            width: 100%;
            height: 0;
            z-index: 3;
        }

        .pagination {
            margin-top: 16px;
        }

        :deep(.el-pagination .el-pager li:not(.is-active)) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;

        }

        :deep(.el-pagination .el-pager li:not(.is-disabled).is-active) {
            border-radius: 1px;
            border: 1px solid rgba(255, 198, 26, 1);
            background: rgba(255, 198, 26, 0.15);

            color: #47ebeb;
            text-align: center;

            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: rgba(255, 255, 255, 1);

        }

        :deep(.el-pagination .el-pager li:hover) {
            border-radius: 1px;
            border: 1px solid rgba(255, 198, 26, 1);
            background: rgba(255, 198, 26, 0.15);

            color: #47ebeb;
            text-align: center;

            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            color: rgba(255, 255, 255, 1);
        }

        :deep(.el-pagination .btn-next) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }

        :deep(.el-pagination .btn-prev) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }

        :deep(.el-pagination__total) {
            background-color: transparent;
            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
text-align: right;
vertical-align: middle;
color: rgba(26, 159, 255, 1);
        }
    }
    .operateButton{
        color:rgba(128, 234, 255, 1)
    }
    }
}
</style>
