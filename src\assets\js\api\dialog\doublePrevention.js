import { fetch } from "../../request";
//获取风险管控清单分页列表
export const risk_control_check_list = (params) =>fetch("/safety-v2/risk-control-check-list/page", { ...params }, "GET");
//字典
export const control_measure_dict = (params) =>fetch(`/safety-v2/control-measure-dict/select-detail/${params}`, { ...params }, "GET");
//获取风险分析对象分页列表
export const risk_object = (params) =>fetch("/safety-v2/risk-object/page", { ...params }, "GET");
//获取部门树
export const get_department_tree = (params) =>fetch("/information-v2/enterprise-department/get-department-tree", { ...params }, "GET");
//获取风险分析单元分页列表
export const risk_analysis_unit = (params) =>fetch("/safety-v2/risk-analysis-unit/page", { ...params }, "GET");
//获取风险事件信息分页列表
export const risk_event_info = (params) =>fetch("/safety-v2/risk-event-info/page", { ...params }, "GET");
//获取风险管控措施分页列表
export const risk_control_measure = (params) =>fetch("/safety-v2/risk-control-measure/page", { ...params }, "GET");
//查询隐患排查任务分页列表
export const task_distribution = (params) =>fetch("/safety-v2/task-distribution/page", { ...params }, "GET");
//获取所有任务类型枚举项--隐患排查任务
export const task_type_enum = (params) =>fetch("/safety-v2/enum/task-type", { ...params }, "GET");
//获取所有排查任务状态枚举项--隐患排查任务
export const task_state_enum = (params) =>fetch("/safety-v2/enum/task-state", { ...params }, "GET");
//获取隐患治理清单分页列表
export const danger_report = (params) =>fetch("/safety-v2/danger-report/page", { ...params }, "GET");
//获取所有隐患来源枚举项--隐患治理清单
export const danger_source_enum = (params) =>fetch("/safety-v2/enum/danger-source", { ...params }, "GET");
//获取所有隐患类型枚举项--隐患治理清单
export const risk_type_enum = (params) =>fetch("/safety-v2/enum/risk-type", { ...params }, "GET");
//获取所有隐患等级枚举项--隐患治理清单
export const danger_level_enum = (params) =>fetch("/safety-v2/enum/danger-level", { ...params }, "GET");
//获取所有治理类型枚举项--隐患治理清单
export const reform_type_enum = (params) =>fetch("/safety-v2/enum/reform-type", { ...params }, "GET");
//获取所有隐患类别枚举项--隐患治理清单
export const risk_category_enum = (params) =>fetch("/safety-v2/enum/risk-category", { ...params }, "GET");
//获取所有隐患状态枚举项--隐患治理清单
export const danger_reform_enum = (params) =>fetch("/safety-v2/enum/danger-reform", { ...params }, "GET");
//分页查询隐患督办通知信息--线上督办记录
export const danger_remind_record = (params) =>fetch("/safety-v2/danger-remind-record/page", { ...params }, "GET");
//分页查询短信发送记录信息--短信发送记录
export const sms_send_info = (params) =>fetch("/safety-v2/sms-send-info/page", { ...params }, "GET");
//分页查询线上线下融合检查
export const offline_check = (params) =>fetch("/safety-v2/offline-check/select-page", { ...params }, "GET");