<svg width="48" height="89" viewBox="0 0 48 89" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="24" cy="24" r="24" fill="#05202E" fill-opacity="0.6"/>
<circle cx="24" cy="24" r="24" fill="url(#paint0_radial_263_16)"/>
<g clip-path="url(#clip0_263_16)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M23.5909 14.8182L20 22L23 23L21 28V33.5H14.5V26V24V21.8889L23.5909 14.8182ZM27.1283 16.9331L33.5 21.8889V24V33.5H27V28H21L27 22L24 21L27.1283 16.9331Z" fill="white"/>
</g>
<ellipse cx="24" cy="84" rx="12" ry="4" fill="#30ABE8" fill-opacity="0.6" stroke="#30ABE8" stroke-width="0.5" stroke-dasharray="2 2"/>
<ellipse cx="24" cy="84" rx="3.75" ry="1.33333" fill="#47EBEB" fill-opacity="0.6" stroke="#30ABE8" stroke-width="0.5"/>
<path d="M23 47.5H25V81.5H23L23 47.5Z" fill="url(#paint1_linear_263_16)"/>
<defs>
<radialGradient id="paint0_radial_263_16" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(24 24) rotate(90) scale(24)">
<stop offset="0.165778" stop-color="#30ABE8" stop-opacity="0"/>
<stop offset="1" stop-color="#30ABE8"/>
</radialGradient>
<linearGradient id="paint1_linear_263_16" x1="24" y1="49" x2="24" y2="83" gradientUnits="userSpaceOnUse">
<stop stop-color="#30ABE8"/>
<stop offset="1" stop-color="#47EBEB" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_263_16">
<rect width="24" height="24" fill="white" transform="translate(12 12)"/>
</clipPath>
</defs>
</svg>
