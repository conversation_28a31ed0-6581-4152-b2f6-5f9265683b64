<template>
  <div class="check-detail-wrapper">
    <div class="top-select">
        <div class="option-item">
            <el-select
                v-model="cameraId"
                class="m-2"
                placeholder="请选择摄像头"
                @change="changeCamera"
                filterable
            >
                <el-option
                    v-for="item in cameraOptions"
                    :key="item.equipmentId"
                    :label="item.equipmentName"
                    :value="item.equipmentId"
                />
            </el-select>
        </div>
    </div>
    <div class="video">
                <div ref="img" id="imgCCC" class="img">
                    <div v-if="videoGrey" class="img-no">暂无数据</div>
                </div>
            </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed, defineEmits, toRefs, watch, getCurrentInstance,
 } from "vue";
import { camera_flv } from "@/assets/js/api/dialog/specialAssignments";
const { proxy } = getCurrentInstance();

const emit = defineEmits(["closeDialog"]);
const props = defineProps({
    dataObj: {
        type: [Object],
    },
});
const { dataObj } = toRefs(props);
let cameraOptions = ref([]);
let cameraId = ref(null);
var jessibuca = null;
const img = ref(null);
let videoGrey = ref(false);
const getFlv = () => {
    camera_flv({
        equipmentIdList: [cameraId.value],
    }).then((res3) => {
        // hazardDialog.value = true
        // myDiv.style.display = "block";
        jessibuca = new JessibucaPro({
            container: img.value,
            videoBuffer: 0.2, // 缓存时长
            isResize: false,
            decoder: "/Jessibuca/decoder-pro.js",
            text: "",
            loadingText: "加载中",
            showBandwidth: true, // 显示网速
            operateBtns: {
                fullscreen: false,
                screenshot: false,
                play: false,
                audio: false,
                performance: false,
            },
            forceNoOffscreen: true,
            isNotMute: false,
            heartTimeout: 10,
            ptzClickType: "mouseDownAndUp",
        });
        console.log(res3);
        if (res3.data.data[0].wsFlvAddress && res3.data.data[0].wsFlvAddress != '离线') {
            jessibuca.play(
                proxy.$changeUrl(res3.data.data[0].wsFlvAddress)
            );
            videoGrey.value = false
        } else {
            videoGrey.value = true;
        }
            
    });
}
const changeCamera = () =>{
    destroy();
    getFlv();
}
const destroy = () => {
    if (jessibuca) {
        // this.jessibuca=null

        jessibuca.destroy();
        jessibuca.value = null;
    }
};
onMounted(()=>{
    if(dataObj.value&&dataObj.value.relateCamera&&dataObj.value.relateCamera.length>0){
        cameraOptions.value = dataObj.value.relateCamera;
        cameraId.value = dataObj.value.relateCamera[0].equipmentId;
        console.log(cameraId.value,'cameraId');
        getFlv();
    }
})
</script>

<style lang="less" scoped>
.check-detail-wrapper{
    width: 760px;
    height: 568px;
    padding-top: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    .top-select{
        .option-item {
                    display: flex;
                    gap: 12px;
                    align-items: center;
                }
                .title {
                    // padding-right: 12px;
                    line-height: 40px; /* 157.143% */
                }
                .el-input {
                    width: 272px;
                    height: 40px;
                }
                .el-select {
                    width: 272px;
                    height: 40px;

                }
                :deep(.el-cascader) {
                    width: 272px !important;
                }
                :deep(.el-select__wrapper){
                    height: 40px !important;
                    padding: 10px 12px !important;
                }
                .el-date-editor {
                    width: 272px;
                }
                
                :deep(.el-input__wrapper) {
                    height: 20px;
                    padding: 10px 12px;
                }
                /* 复选框容器样式 */
                .checkbox-container {
                    display: flex;
                    align-items: center;
                }

                .custom-checkbox-group {
                    display: flex;
                    flex-wrap: wrap;
                }

                /* 黄色复选框样式 - 选中状态 */
                :deep(.yellow-checkbox .el-checkbox__input.is-checked .el-checkbox__inner) {
                    background-color: rgba(255, 198, 26, 0.3) !important;
                    border-color: rgba(255, 198, 26, 1) !important;
                }

                :deep(.yellow-checkbox .el-checkbox__input.is-checked .el-checkbox__inner::after) {
                    border-color: rgba(255, 255, 255, 1) !important;
                }
    }
    .video {

        width: 760px;
        height: 428px;
        flex-shrink: 0;
        background: rgba(48, 171, 232, 0.15);
        // margin-top: 17px;
        .img {
            width: 760px;
        height: 428px;
            background: rgba(48, 171, 232, 0.15);
            display: flex;
            justify-content: center;
            .img-no {
                margin: auto 0;
                color: #fff;
                text-align: center;
                font-family: "Noto Sans SC";
                font-size: 20px;
                font-style: normal;
                font-weight: 400;
                line-height: 28px; /* 140% */
            }
        }
    }
}
</style>