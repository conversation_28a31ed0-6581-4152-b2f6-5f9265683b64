<template>
  <div>
    <div class="nav-all-wrapper">
      <!-- 企业选择器 -->
      <div class="input-wrapper">
        <el-select
          v-model="enterpriseName"
          class="m-2"
          placeholder="请选择"
          clearable
          @change="changeEnterpriseName"
          filterable
        >
          <el-option
            v-for="item in enterpriseNameOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      
      <!-- 导航按钮 -->
      <div v-if="showFlag == 2 || showFlag == 3" class="nav-all-btns">
        <div
          :class="active === 1 ? 'active-btn' : 'btn'"
          @click="setPage(1)"
        >
          <img
            class="icon"
            src="@/assets/newImages/Nav/SecureBase/enterprise.svg"
          />
          <div class="text">企业档案</div>
        </div>
        <div
          :class="active === 2 ? 'active-btn' : 'btn'"
          @click="setPage(2)"
        >
          <img
            class="icon"
            src="@/assets/newImages/Nav/SecureBase/basic.svg"
          />
          <div class="text">基础设施</div>
        </div>
        <div
          :class="active === 3 ? 'active-btn' : 'btn'"
          @click="setPage(3)"
        >
          <img
            class="icon"
            src="@/assets/newImages/Nav/SecureBase/sensitive.svg"
          />
          <div class="text">敏感目标</div>
        </div>
      </div>
    </div>
    <!-- 企业档案弹窗 -->
    <files-dialog v-if="filesDialogShow" :pickId="currentParams" @closeDialog="closeDialog"></files-dialog>
    <!-- 基础设施弹窗 -->
    <infrastructure-dialog v-if="informationDialogShow" :pickId="currentParams" @closeDialog="closeDialog"></infrastructure-dialog>
    <!-- 敏感目标 -->
    <surrounding-environment-dialog v-if="surroundingDialogShow" :pickId="currentParams" @closeDialog="closeDialog" ></surrounding-environment-dialog>
    <!-- 特殊工作详情弹窗 -->
    <!-- <special-work 
      v-if="specialWorkShow" 
      :sonId="currentParams" 
      @closeDialog="closeDialog"
    /> -->
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, getCurrentInstance, nextTick } from "vue";
import * as Cesium from "cesium";
import { enterprise_list, nj_equipments, sensitive_target_list } from "@/assets/js/api/parkArchives";
import { select_page } from "@/assets/js/api/dialog/specialAssignments";
// import SpecialWork from "./Dialog/navDialog/specialWork/index.vue";
// POI图片路径
import progressBlueImg from "/static/newPoi/SpecialAssignments/progress-blue.png";
import completedBlueImg from "/static/newPoi/SpecialAssignments/completed-blue.png";
//企业档案POI-蓝色
import enterpriseBlueImg from "/static/newPoi/SecureBase/enterprise-blue.png";
//基础设施POI-蓝色
import basicBlueImg from "/static/newPoi/SecureBase/basic-blue.png";
//基础设施POI-蓝色
import sensitiveBlueImg from "/static/newPoi/SecureBase/sensitive-blue.png";
//弹窗子组件
//企业档案
import filesDialog from "../ParkArchives/subgroup/filesDialog.vue";
//基础设施
import infrastructureDialog from "../ParkArchives/subgroup/infrastructureDialog.vue";
//敏感目标
import surroundingEnvironmentDialog from "../ParkArchives/subgroup/SurroundingEnvironmentDialog.vue"
// import filesDialog from "../ParkArchives/subgroup/filesDialog.vue";
// 组件实例和状态
const { proxy } = getCurrentInstance();
const specialWorkShow = ref(false);
//企业档案弹窗展示
let filesDialogShow = ref(false);
//基础设施弹窗展示
let informationDialogShow = ref(false);
//敏感目标弹窗展示
let surroundingDialogShow = ref(false)
const currentParams = ref(null);
const enterpriseName = ref("");
const enterpriseNameOptions = ref([]);
const showFlag = ref(1);
const active = ref('');
const positionData = ref([]);
const areaDataThree = ref([]);

// 获取企业列表数据
const fetchEnterpriseData = async () => {
  try {
    const res = await enterprise_list({enterprise_name:""});
    if (res.data.success && res.data.data?.length) {
      enterpriseNameOptions.value = res.data.data.map(x => ({
        value: x.enterprise_name,
        label: x.enterprise_name,
      }));
    }
  } catch (error) {
    console.error("获取企业列表失败:", error);
  }
};

// 切换导航按钮
const setPage = (val) => {
  if (val === active.value) {
    // 再次点击同一按钮，取消选择
    active.value = '';
    clearMapEntities();
    initBoundary();
  } else {
    clearMapEntities();
    active.value = val;
    // 根据选择获取不同状态的数据
    getData(val);
  }
};

// 清除地图实体
const clearMapEntities = () => {
  window.viewer.entities.removeAll();
  window.map1.clearMap();
  if (window.cluster) window.cluster.setMap(null);
};

// 切换企业
const changeEnterpriseName = (val) => {
  if (val) {
    const searchItem = enterpriseNameOptions.value.find(i => i.value === val);
    enterpriseName.value = searchItem?.label || '';
  } else {
    enterpriseName.value = '';
  }
  
  clearMapEntities();
  
  // 如果有选中的导航按钮，重新获取数据
  getData(active.value)
};

// 获取点位信息
const getData = async (val) => {
  positionData.value = [];
  if(val == 1){
    try {
        const res = await enterprise_list({enterprise_name:enterpriseName.value});
        
        if (res.data.success && res.data.data?.length) {
        // 处理返回的数据
        res.data.data.forEach(item => {
            if (item.longitude && item.latitude) {
            positionData.value.push({
                name: item.enterprise_name,
                id: item.id,
                longitude: Number(item.longitude),
                latitude: Number(item.latitude),
                dialogParams: item
            });
            }
        });
        
        // 根据当前地图模式显示点位
        renderMapPoints();
        }
    } catch (error) {
        console.error("获取点位数据失败:", error);
    }
  }else if(val == 2){
    try {
        const res = await nj_equipments({enterprise_name:enterpriseName.value});
        
        if (res.data.success && res.data.data?.length) {
        // 处理返回的数据
        res.data.data.forEach(item => {
            if (item.longitude && item.latitude) {
            positionData.value.push({
                name: item.equipment_name,
                id: item.id,
                longitude: Number(item.longitude),
                latitude: Number(item.latitude),
                dialogParams: item
            });
            }
        });
        
        // 根据当前地图模式显示点位
        renderMapPoints();
        }
    } catch (error) {
        console.error("获取点位数据失败:", error);
    }
  }else{
    try {
        const res = await sensitive_target_list({enterprise_name:enterpriseName.value});
        
        if (res.data.success && res.data.data?.length) {
        // 处理返回的数据
        res.data.data.forEach(item => {
            if (item.longitude && item.latitude) {
            positionData.value.push({
                name: item.sensitive_name,
                id: item.id,
                longitude: Number(item.longitude),
                latitude: Number(item.latitude),
                dialogParams: item
            });
            }
        });
        
        // 根据当前地图模式显示点位
        renderMapPoints();
        }
    } catch (error) {
        console.error("获取点位数据失败:", error);
    }
  }
 
};

// 根据当前地图模式渲染点位
const renderMapPoints = () => {
  if (positionData.value.length === 0) return;
  
  const useImg = active.value === 1 ? enterpriseBlueImg : active.value== 2 ? basicBlueImg: sensitiveBlueImg;
  let showDialog = '';
  if(active.value== 1){
    showDialog= 'filesDialogShow';
  }else if(active.value == 2){
    showDialog = 'informationDialogShow'
  }else{
    showDialog='surroundingDialogShow'
  }
  if (window.toggle === 2) {
    // Cesium 3D模式
    renderCesiumPoints(useImg,showDialog);
  } else if (window.toggle === 3) {
    // 2D地图模式
    render2DMapPoints(useImg,showDialog);
  }
};

// 渲染Cesium 3D点位
const renderCesiumPoints = (img, showDialog) => {
  // 预加载图片以解决跨域问题
  const imageElement = new Image();
  imageElement.crossOrigin = 'anonymous';
  imageElement.src = img;
  
  imageElement.onload = () => {
    positionData.value.forEach(data => {
      const wgsCoords = proxy.$transformGCJ02ToWGS84(
        Number(data.latitude), 
        Number(data.longitude)
      );
      
      window.viewer.entities.add({
        name: data.id,
        dialogParams: data.dialogParams,
        longitude: Number(data.longitude),
        latitude: Number(data.latitude),
        position: Cesium.Cartesian3.fromDegrees(
          wgsCoords.lng,
          wgsCoords.lat,
          10
        ),
        billboard: {
          image: imageElement,
          width: 48,
          height: 62,
          disableDepthTestDistance: Number.POSITIVE_INFINITY,
          pixelOffset: new Cesium.Cartesian2(0, -31),
        },
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        scaleByDistance: new Cesium.NearFarScalar(0, 0, 1, 1),
        pixelOffsetScaleByDistance: new Cesium.NearFarScalar(0, 0, 1, 1),
      });
    });
    
    // 设置点击事件
    setupCesiumClickEvent(showDialog);
    
    // 调整视野
    adjustField();
  };
  
  imageElement.onerror = () => {
    console.error('图片加载失败，使用备用方案');
    // 备用方案：直接使用图片路径
    positionData.value.forEach(data => {
      const wgsCoords = proxy.$transformGCJ02ToWGS84(
        Number(data.latitude), 
        Number(data.longitude)
      );
      
      window.viewer.entities.add({
        name: data.id,
        dialogParams: data.dialogParams,
        longitude: Number(data.longitude),
        latitude: Number(data.latitude),
        position: Cesium.Cartesian3.fromDegrees(
          wgsCoords.lng,
          wgsCoords.lat,
          10
        ),
        billboard: {
          image: img,
          width: 48,
          height: 62,
          disableDepthTestDistance: Number.POSITIVE_INFINITY,
          pixelOffset: new Cesium.Cartesian2(0, -31),
        },
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      });
    });
    
    setupCesiumClickEvent(showDialog);
    adjustField();
  };
};

// 设置Cesium点击事件
const setupCesiumClickEvent = (showDialog) => {
  window.viewer.screenSpaceEventHandler.setInputAction(
    function (e) {
      const pick = window.viewer.scene.pick(e.position);
      if (pick && pick.id && pick.id._dialogParams) {
        clickOpenDialog(pick.id._dialogParams, showDialog);
      }
    },
    Cesium.ScreenSpaceEventType.LEFT_CLICK
  );
};

// 渲染2D地图点位
const render2DMapPoints = (img,showDialog) => {
  positionData.value.forEach(item => {
    addMarker(item, img, showDialog);
  });
  window.map1.setFitView();
};

// 2D地图添加标记点
const addMarker = (item, imgUrl, showDialog) => {
  const marker = new AMap.Marker({
    icon: new AMap.Icon({
      size: new AMap.Size(48, 62),
      image: imgUrl,
      imageSize: new AMap.Size(48, 62),
    }),
    position: [item.longitude, item.latitude],
    offset: new AMap.Pixel(-24, -62),
  });

  // 点击事件
  marker.on("click", () => {
    clickOpenDialog(item.dialogParams, showDialog);
  });

  // 悬浮提示框样式
  const boxStyle = `
    width: 120px;
    height: 40px;
    border: 2px solid #47EBEB;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    line-height: 40px;
  `;
  
  const textStyle = `
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #FFFFFF;
    font-size: 14px;
  `;

  // 创建信息窗口
  const infoWindow = new AMap.InfoWindow({
    isCustom: true,
    content: `<div style="${boxStyle}"><div style="${textStyle}">${item.name}</div></div>`,
    offset: new AMap.Pixel(0, -85),
  });

  // 鼠标悬浮事件
  marker.on("mouseover", (e) => {
    infoWindow.open(window.map1, e.target.getPosition());
  });

  marker.on("mouseout", () => {
    infoWindow.close();
  });

  marker.setMap(window.map1);
};

// 点击打开弹窗
const clickOpenDialog = (item, showDialog) => {
    console.log(item, showDialog,'打开弹窗');
    
  if (showDialog === 'filesDialogShow') {
    currentParams.value = item.enterprise_name;
    filesDialogShow.value = true;
  }else if(showDialog == 'informationDialogShow'){
    currentParams.value = item.id;
    informationDialogShow.value = true;
  }else{
    currentParams.value = item.id;
    surroundingDialogShow.value = true;
  }
};

// 调整Cesium视野范围
const adjustField = () => {
  if (positionData.value.length === 0) return;
  
  const arrs = []; // 经纬度数组
  const lonArr = []; // 经度数组
  const latArr = []; // 纬度数组

  // 收集所有点位的经纬度
  positionData.value.forEach(item => {
    arrs.push(item.longitude);
    lonArr.push(item.longitude);
    arrs.push(item.latitude);
    latArr.push(item.latitude);
  });

  // 处理区域数据
  if (areaDataThree.value.length > 0) {
    areaDataThree.value.forEach(item => {
      for (let i = 0; i < item.path.length; i++) {
        if (i % 3 === 0) {
          arrs.push(item.path[i]);
          lonArr.push(item.path[i]);
        } else if (i % 3 === 1) {
          arrs.push(item.path[i]);
          latArr.push(item.path[i]);
        }
      }
    });
  }

  // 计算中心点和视野范围
  const adr = Cesium.Cartesian3.fromDegreesArray(arrs);
  const polys = Cesium.BoundingSphere.fromPoints(adr).center;
  const surfacePoint = Cesium.Ellipsoid.WGS84.scaleToGeodeticSurface(polys);
  const ellipsoid = window.viewer.scene.globe.ellipsoid;
  const cartesian = new Cesium.Cartesian3(surfacePoint.x, surfacePoint.y, surfacePoint.z);
  const cartographic = ellipsoid.cartesianToCartographic(cartesian);

  // 计算中心点经纬度
  const centerPoint = {
    lat: Cesium.Math.toDegrees(cartographic.latitude),
    lng: Cesium.Math.toDegrees(cartographic.longitude),
    alt: cartographic.height
  };

  // 计算视野范围
  const maxLon = Math.max(...lonArr);
  const minLon = Math.min(...lonArr);
  const maxLat = Math.max(...latArr);
  const minLat = Math.min(...latArr);
  const latDiff = maxLat - minLat;
  const lonDiff = maxLon - minLon;
  let radius = Math.max(latDiff, lonDiff);
  
  // 根据范围大小调整高度
  let height;
  if (radius === 0) {
    radius = 0.002;
    height = radius * 1150000;
  } else if (radius > 1) {
    height = radius * 200000;
  } else if (radius < 0.004) {
    height = radius * 1150000;
  } else if (radius < 0.002) {
    height = radius * 9550000;
  } else {
    height = radius * 550000;
  }

  // 设置相机视角
  nextTick(() => {
    window.viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(centerPoint.lng, centerPoint.lat, height),
    });
  });
};

// 园区边界坐标
const parkBoundaryPath = [
  [115.107027873843, 37.967476971868],
  [115.108984044575, 37.9676990161805],
  [115.114581935149, 37.9682895127299],
  [115.114875013849, 37.9647435975399],
  [115.120760157275, 37.9654807783805],
  [115.122757109275, 37.9648187005449],
  [115.126154890766, 37.9638912590139],
  [115.127570772583, 37.9589187898429],
  [115.135388661825, 37.9593751355947],
  [115.140649555561, 37.9557639827728],
  [115.140552198161, 37.9511191584419],
  [115.140682599355, 37.9498663851139],
  [115.127644524961, 37.9465171855449],
  [115.127531161592, 37.9469572161843],
  [115.116666868099, 37.9455974855667],
  [115.115731255449, 37.9503398556584],
  [115.110223040793, 37.9505120787165],
  [115.106759744913, 37.9505159984926],
  [115.107027595251, 37.9674533646081],
  [115.107027873843, 37.967476971868]
];

// 绘制园区边界
let parkBoundaryPolyline = null;
const initBoundary = () => {
  if (window.map1 != null && window.toggle == 3) {
    parkBoundaryPolyline = new AMap.Polyline({
      path: parkBoundaryPath,
      strokeColor: "#49D1AF",
      strokeWeight: 3,
      strokeOpacity: 0.9,
      strokeStyle: "dashed",
      zIndex: 50,
      bubble: true,
    });
    
    window.map1.add([parkBoundaryPolyline]);
    window.map1.setFitView();
  }
};

// 关闭弹窗
const closeDialog = (value) => {
    //企业档案
  if (value === "enterprise") {
    filesDialogShow.value = false;
  }else if(value =='infrastructure'){
    informationDialogShow.value = false;
  }else{
    surroundingDialogShow.value = false;
  }
};

// 生命周期钩子
onMounted(() => {
  // 监听地图切换事件
  proxy.$bus.on("change_toggle", (val) => {
    showFlag.value = val;
    active.value = '';
    clearMapEntities();
    initBoundary();
  });

  // 初始化
  initBoundary();
  fetchEnterpriseData();
  showFlag.value = window.toggle;
  
  // 关闭热力图
  if (window.heatmap != null) {
    window.heatmap.setMap(null);
  }
});
onUnmounted(() => {
  proxy.$bus.off("change_toggle");
});


</script>
<style lang="less" scoped>
.nav-all-wrapper{
    // width: 928px;
    height: 862px;
    position: absolute;
    z-index: 1000;
    top: 96px;
    left: 496px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    pointer-events: none;
    .nav-all-btns{
        display: flex;
        flex-direction: column;
        gap:24px;
        pointer-events: auto;
        .btn {
            width: 144px;
            height: 34px;
            background: url("@/assets/newImages/Nav/nav-btn-blue.svg") no-repeat;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
        }

        .active-btn {
            width: 144px;
            height: 34px;
            background: url("@/assets/newImages/Nav/nav-active-btn-yellow.svg") no-repeat;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
        }
        .icon {
            width: 24px;
            height: 24px;
            // margin: auto 0;
            // margin-top: 4px;
            margin-left: 12px;
        }

        .text {
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
            vertical-align: middle;
            color: #FFFFFF;

            margin: auto 0;
            margin-left: 24px;
        }
    }
    .input-wrapper {
        pointer-events: auto;

    .el-input {
                    width: 210px;
                    height: 40px;
                }
                .el-select {
                    width: 210px;
                    height: 40px;

                }
                :deep(.el-cascader) {
                    width: 210px !important;
                }
                :deep(.el-select__wrapper){
                    height: 40px !important;
                    padding: 10px 12px !important;
                }
                .el-date-editor {
                    width: 210px;
                }
                
                :deep(.el-input__wrapper) {
                    height: 20px;
                    padding: 10px 12px;
                }
                /* 复选框容器样式 */
                .checkbox-container {
                    display: flex;
                    align-items: center;
                }

                .custom-checkbox-group {
                    display: flex;
                    flex-wrap: wrap;
                }

                /* 黄色复选框样式 - 选中状态 */
                :deep(.yellow-checkbox .el-checkbox__input.is-checked .el-checkbox__inner) {
                    background-color: rgba(255, 198, 26, 0.3) !important;
                    border-color: rgba(255, 198, 26, 1) !important;
                }

                :deep(.yellow-checkbox .el-checkbox__input.is-checked .el-checkbox__inner::after) {
                    border-color: rgba(255, 255, 255, 1) !important;
                }
              
}



}
</style>

<style lang="less" scoped>


.noactive {
    width: 156px;
    height: 34px;
    background: url("../../../assets/images/jump-btn/nav-noactive-big.svg")
        no-repeat;
    background-size: cover;
    background-position: center;
    display: flex;
}




</style>
