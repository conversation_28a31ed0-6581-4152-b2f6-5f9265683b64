<template>
  <div class="check-detail-wrapper">
    <div class="check-small-wrapper">
        <div class="check-item-small">
            <div class="check-item-title">作业执行人：</div>
            <div class="check-item-value">{{dataObj&&dataObj.executor?dataObj.executor:'-'}}</div>
        </div>
        <div class="check-item-small">
            <div class="check-item-title">执行人手机：</div>
            <div class="check-item-value">{{dataObj&&dataObj.executorPhone?dataObj.executorPhone:'-'}}</div>
        </div>
        <div class="check-item-small">
            <div class="check-item-title">作业监护人：</div>
            <div class="check-item-value">{{dataObj&&dataObj.worker?dataObj.worker:'-'}}</div>
        </div>
        <div class="check-item-small">
            <div class="check-item-title">监护人手机：</div>
            <div class="check-item-value">{{dataObj&&dataObj.workerPhone?dataObj.workerPhone:'-'}}</div>
        </div>
    </div>
    <div class="check-item">
        <div class="check-item-title">作业方案：</div>
        <div class="check-item-value-big" v-if="dataObj&&dataObj.workFiles&&dataObj.workFiles.length>0">
            <div class="check-item-value-big-inner" v-for="item in dataObj.workFiles" :key="item.id">
                {{item.name}}
            </div>

        </div>
        <div class="check-item-value-big" v-else>-</div>
    </div>
    <div class="check-map-wrapper">
        <div class="check-item-title">作业位置：</div>
        <div class="check-map" id="checkmap">
        </div>
    </div>
    <div class="check-small-wrapper">
        <div class="check-item-small">
            <div class="check-item-title">安全交底人：</div>
            <div class="check-item-value">{{dataObj&&dataObj.safetyHead?dataObj.safetyHead:'-'}}</div>
        </div>
        <div class="check-item-small">
            <div class="check-item-title">接受交底人：</div>
            <div class="check-item-value">{{dataObj&&dataObj.receiveHead?dataObj.receiveHead:'-'}}</div>
        </div>
        <div class="check-item-small">
            <div class="check-item-title">作业票号：</div>
            <div class="check-item-value">{{dataObj&&dataObj.billSn?dataObj.billSn:'-'}}</div>
        </div>
        <div class="check-item-small">
            <div class="check-item-title">开票时间：</div>
            <div class="check-item-value">{{dataObj&&dataObj.billTime?dataObj.billTime:'-'}}</div>
        </div>
    </div>
    <div class="check-item">
        <div class="check-item-title">作业票：</div>
        <div class="check-item-value-big" v-if="dataObj&&dataObj.billFile&&dataObj.billFile.length>0">
            <div class="check-item-value-big-inner" v-for="item in dataObj.billFile" :key="item.id">
                {{item.name}}
            </div>

        </div>
        <div class="check-item-value-big" v-else>-</div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed, defineEmits, toRefs, nextTick, watch } from "vue";
import { check_detail } from "@/assets/js/api/dialog/specialAssignments";

const emit = defineEmits(["closeDialog"]);
const props = defineProps({
    dataObj: {
        type: [Object],
    },
});
const radioArr = ref([10,8,6,4,2,0])
const { dataObj } = toRefs(props);
let map = null;

const initMap = () =>{
    map = new AMap.Map("checkmap", {
        //设置地图容器id
        viewMode: "3D", //是否为3D地图模式
        zoom: 12, //初始化地图级别
        mapStyle: "amap://styles/db6d462a4571314863ef03382b234c33",
        //   layers: [new AMap.TileLayer.Satellite()],
        center: [115.36665, 37.76225], //初始化地图中心点位置
    });
    console.log(dataObj.value.location&&dataObj.value.location.lat&&dataObj.value.location.lng,'hhahahhahahhahhaha');
    
    if (dataObj.value.location?.lat && dataObj.value.location?.lng) {
        const lng = Number(dataObj.value.location.lng);
        const lat = Number(dataObj.value.location.lat);

        console.log("打点坐标:", lng, lat);

        // 3. 创建 Marker
        const marker = new AMap.Marker({
            position: [lng, lat],  // 注意顺序：[经度, 纬度]
            offset: new AMap.Pixel(-15, -10),  // 调整偏移量
            zIndex: 160,
        });

        // 4. 添加 Marker 到地图
        marker.setMap(map);

        // 5. 调整视角到 Marker 的位置（可选动画）
        map.setCenter([lng, lat]);  // 直接跳转
        // 或使用动画平滑移动：
        // map.panTo([lng, lat], { duration: 1000 });  // 1秒动画
    } else {
        console.error("坐标数据无效:", dataObj.value.location);
    }
    
}
watch(
    dataObj,
    (a, b) => {
        console.log(a, b,'监听');

        if(a&&a.location){
            console.log('youle');
            nextTick(()=>{
    initMap();

    })
        }
        
    },
    {
        immediate: true,
        deep:true
    },
);
onMounted(()=>{
    console.log(dataObj.value,'详情---------');
    
})
</script>

<style lang="less" scoped>
.check-detail-wrapper{
    width: 760px;
    height: 584px;
    .check-small-wrapper{
        width: 760px;
    height: 80px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    }
    .check-item-small{
        width: 370px;
    height: 40px;
    font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: rgba(255, 255, 255, 1);
display: flex;
align-items: center;
    }
    .check-item-title{
color: rgba(128, 234, 255, 1);

    }
    .check-item-value{
    }
    .check-item{
        width: 760px;
    height: 50px;
    font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: rgba(255, 255, 255, 1);
display: flex;
align-items: center;
    }
    .check-item-value-big{}
    .check-item-value-big{
        width: 664px;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }
    .check-map-wrapper{
        width: 748px;
height: 260px;

        display: flex;
        .check-map{
width: 664px;
height: 260px;
border-radius: 4px;
background-color: #05202E4D;

        }
    }
}
</style>