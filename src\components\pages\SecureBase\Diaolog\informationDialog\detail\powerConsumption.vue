<template>
    <div class="bottom-all-wrapper">
        <div class="top-btns">
               <div :class="topActive=='实时数据'?'active-top-btn':'top-btn'" @click="clickBtn('实时数据')">实时数据</div>         
               <div :class="topActive=='历史数据'?'active-top-btn':'top-btn'" @click="clickBtn('历史数据')">历史数据</div>         
        </div>
        <div class="bottom" v-if="topActive=='实时数据'">
            <el-table class="tablebox" :data="tableData" style="width: 100%">
                <el-table-column
                    prop="enterpriseName"
                    label="企业名称"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="equipmentId"
                    label="设备ID"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="statisticValue"
                    label="用电量读书(KWH)"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="reportTime"
                    label="上报时间"
                    show-overflow-tooltip
                />
            </el-table>
            <el-pagination
                class="pagination"
                background
                v-model:currentPage="pageNum"
                :page-size="pageSize"
                @current-change="handleCurrentChange"
                layout="->,total, prev, pager, next"
                :total="total"
            />
        </div>
        <div class="bottom" v-else>
            <div class="top-content">
                        <div class="select-option">
                            <div class="option-item">
                                <div class="title">状态</div>
                                <el-select
                                    v-model="queryParams.periodCode"
                                    class="m-2"
                                    placeholder="请选择"
                                    @change="changeState"
                                    filterable
                                >
                                    <el-option
                                        v-for="item in btnOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </div>
                            <div class="option-item" v-if="queryParams.periodCode==1">
                                <div class="title">选择日期</div>
                                <el-date-picker
                                    v-model="dateValue"
                                    type="daterange"
                                    range-separator="-"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    clearable
                                    value-format="YYYY-MM-DD"
                                    @change="changeDate"
                                />
                            </div>
                            <div class="option-item" v-if="queryParams.periodCode==0">
                                <div class="title">选择时间</div>
                                <el-time-picker
                                    v-model="timeValue"
                                    is-range
                                    range-separator="-"
                                    start-placeholder="开始时间"
                                    end-placeholder="结束时间"
                                    clearable
                                    value-format="HH:mm:ss"
                                    @change="changeTime"
                                />
                            </div>
                        </div>
                        <div class="select-btns">
                            <div class="search-btn" @click="getHistoryData">
                                搜索
                            </div>
                            <div class="reset-btn" @click="resetSearch">
                                重置
                            </div>
                        </div>
                    </div>
                <div class="totalwrap" ref="totalwrap"></div>

        </div>
    </div>
</template>

<script setup>
import {
    equipment_data_current, equipment_data_history
} from "@/assets/js/api/dialog/secureBase";
import * as echarts from "echarts";

import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    computed,
    defineEmits,
    getCurrentInstance,
    toRefs
} from "vue";
const { proxy } = getCurrentInstance();
const props = defineProps({
    sonId: {
        type: [String, Number],
    },
});
const { sonId } = toRefs(props);
const btnOptions = ref([
    {
        value:0,
        label:'按当天小时查询'
    },
    {
        value:1,
        label:'按日期范围查询'
    },
])
let queryParams = ref({
        periodCode:0

});
const tableData = ref([]);
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);
//顶部按钮
let topActive = ref('实时数据');
//切换顶部按钮
let clickBtn = (val) =>{
    topActive.value = val;
    dateValue.value='';
        timeValue.value='';
         queryParams.value = {
            periodCode:0
        };
        if(val=='历史数据'){
            getHistoryData();
        }else{
            getData();
        }
        
    
}
const handleCurrentChange = (val) => {
    pageNum.value = val;
    getData();
};
const resetSearch = () =>{
    queryParams.value = {
        periodCode:0
    };
    getHistoryData();
}
//获取实时数据
const getData = () => {
    equipment_data_current({
        pageNum: pageNum.value,
        pageSize: pageSize.value,
        enterpriseId:sonId.value,
    }).then((res) => {
        console.log(res);
        if (res.data && res.data.data) {
            tableData.value=res.data.data.list;
            total.value = res.data.data.total;
            // tableData.value = res.data.data.list.map((i) => {
            //     return {
            //         ...i,
            //         awardFileObj: i.awardFile ? JSON.parse(i.awardFile) : "",
            //     };
            // });
        }
    });
};
//更改状态
const changeState = (val) =>{
    console.log(val,';;;;;;;;;;;;;;;;;');
    dateValue.value='';
        timeValue.value='';
         queryParams.value = {
            periodCode:0
        };
    getHistoryData();
}
//日期范围
const dateValue = ref("");
//选择日期范围
const changeDate = () => {
    pageNum.value = 1;
    console.log(dateValue.value);
    if (dateValue.value != null) {
        queryParams.value.startDate = dateValue.value[0];
        queryParams.value.endDate = dateValue.value[1];
    } else {
        queryParams.value.startDate = "";
        queryParams.value.endDate = "";
    }

    console.log(queryParams.value);
    getHistoryData();
};
//时间范围
const timeValue = ref("");
//选择时间范围
const changeTime = () => {
    pageNum.value = 1;
    console.log(timeValue.value);
    if (timeValue.value != null) {
        queryParams.value.startTime = timeValue.value[0];
        queryParams.value.endTime = timeValue.value[1];
    } else {
        queryParams.value.startTime = "";
        queryParams.value.endTime = "";
    }

    console.log(queryParams.value);
    getHistoryData();
};
//获取历史数据
const getHistoryData = () => {
    xData.value=[];
    yData.value=[];
    equipment_data_history({
        enterpriseId:sonId.value,
        ...queryParams.value,
    }).then((res) => {
        console.log(res);
        if (res.data && res.data.data) {
            res.data.data.forEach((item)=>{
                if(queryParams.value.periodCode==0){
                    xData.value.push(item.statisticHour);
                }else{
                    xData.value.push(item.statisticDate);
                }
                yData.value.push(item.consumption);
            })
            console.log(xData.value,yData.value,';;;;;;;;;;;;;;');
            if(xData.value.length>0&&yData.value.length>0){
                totalChart = echarts.init(totalwrap.value);
                initChart();
                totalChart.setOption(option);
                window.addEventListener("resize", () => {
                    totalChart.resize();
                });
            }
           
            
        }
    });
};
let option;
let optionRank;
let totalwrap = ref(null);
let totalChart;
let xData = ref([]);
let yData = ref([]);
const initChart = () => {
    option = {
        grid: {
            left: 30,
            right: 60,
            top: 40,
            bottom: 0,
            containLabel: true,
        },
        tooltip: {
            trigger: "axis",
            valueFormatter: function (value) {
                return value + "万千瓦时";
            },
        },
        legend: {
            show: false,
            x: "right", // 图例水平居中
            y: "top", // 图例垂直居上
            itemStyle: { opacity: 0 }, //去圆点
            textStyle: {
                color: "#fff",
            },
        },
        xAxis: [
            {
                type: "category",
                boundaryGap: false,
                axisLabel: {
                    color: "#1AB2FF",
                    textStyle: {
                        color: "#FFFFFF",
                    },
                },
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: "#397cbc",
                    },
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: false,
                    lineStyle: {
                        color: "#195384",
                    },
                },
                data: xData.value,
            },
        ],
        yAxis: [
            {
                type: "value",
                name: "单位：万千瓦时",
                nameTextStyle: {
                    //y轴上方单位的颜色
                    color: "#fff",
                },
                axisLabel: {
                    formatter: "{value}",
                    textStyle: {
                        color: "#FFFFFF",
                    },
                },
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: "#27b4c2",
                    },
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: "rgba(255, 255, 255, 0.15)",
                    },
                },
            },
        ],
        series: [
            {
                name: "超标统计",
                type: "line",
                showSymbol: true,
                symbolSize: 8,
                smooth: true,
                itemStyle: {
                    normal: {
                        color: "#FFC61A",
                        lineStyle: {
                            color: "#FFC61A",
                            width: 1,
                        },
                        areaStyle: {
                            //color: '#94C9EC'
                            color: new echarts.graphic.LinearGradient(
                                0,
                                1,
                                0,
                                0,
                                [
                                    {
                                        offset: 0,
                                        color: "rgba(255, 198, 26, 0.00)",
                                    },
                                    {
                                        offset: 1,
                                        color: "rgba(255, 198, 26, 0.30)",
                                    },
                                ],
                            ),
                        },
                    },
                },
                data: yData.value,
            },
        ],
    };
};
onMounted(() => {
    console.log(sonId.value,'llsssssaaaaaaaaa');
    
    getData();
});
onBeforeUnmount(() => {});
</script>

<style lang="less" scoped>
.bottom-all-wrapper{
    width: 1120px;
    height: 600px;
display: flex;
        flex-direction: column;
        gap:20px;
// background-color: #ffc61a;
}
.top-btns{
    width: 1120px;
    height: 24px;
    display: flex;
    gap:8px;
    font-family: Noto Sans SC;
font-weight: 400;
font-size: 12px;
line-height: 12px;
letter-spacing: 0%;
text-align: center;

    .active-top-btn{
        padding: 4px 8px;
    color:#FFC61A;
border-radius: 2px;
border: 1px solid #FFC61A

        }
        .top-btn{
            padding: 4px 8px;
    color:#80EAFF;
border-radius: 2px;
border: 1px solid #80EAFF
        }

}
.top-content {
            display: flex;
            justify-content: space-between;

            .select-option {
                width: 1120px;
                height: 40px;
                display: flex;
                gap: 20px;
                align-items: center;
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                letter-spacing: 0%;
                text-align: right;
                color: rgba(128, 234, 255, 1);

                .option-item {
                    display: flex;
                    gap: 12px;
                    align-items: center;
                }
                .title {
                    // padding-right: 12px;
                    line-height: 40px; /* 157.143% */
                }
                .el-input {
                    width: 210px;
                    height: 40px;
                }
                .el-select {
                    width: 210px;
                    height: 40px;

                }
                :deep(.el-cascader) {
                    width: 210px !important;
                }
                :deep(.el-select__wrapper){
                    height: 40px !important;
                    padding: 10px 12px !important;
                }
                .el-date-editor {
                    width: 210px;
                }
                
                :deep(.el-input__wrapper) {
                    height: 20px;
                    padding: 10px 12px;
                }
               
            }
            .select-btns {
                width: 192px;
                height: 40px;
                display: flex;
                gap: 16px;
                color: #fff;
                text-align: center;
                font-family: Noto Sans SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 40px; /* 142.857% */
                .search-btn {
                    width: 88px;
                    height: 40px;
                    border-radius: 4px;
                    border: 0.5px solid #30abe8;
                    background: rgba(48, 171, 232, 0.3);
                }
                .reset-btn {
                    width: 88px;
                    height: 40px;
                    border-radius: 4px;
                    border: 0.5px solid #ffc61a;
                    background: rgba(255, 198, 26, 0.3);
                }
            }
            .select2 {
                margin-top: 16px;
            }
        }
.bottom {
    width: 1120px;
    height: 504px;
    .totalwrap {
        width: 1120px;
        height: 464px;
        margin-top: 16px;
    }
    .tablebox {
        //表格四个边框的颜色
        // border: 1px solid #30abe8 !important;
        border: none !important;
        border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;

        th.el-table__cell {
            border: none !important;
        }
    }

    :deep(.el-table),
    :deep(.el-table__expanded-cell) {
        background-color: transparent !important;
        color: #ffffff;
    }

    // 设置表头颜色
    :deep(.el-table th) {
        // border-bottom: 1px solid #30abe8 !important;
        height: 40px;
        background-color: rgba(25, 159, 255, 0.1) !important;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: 0%;
        text-align: center;
        color: rgba(128, 234, 255, 1);
    }

    .el-table__header-wrapper,
    .el-table__fixed-header-wrapper {
        //表头的样式
        th {
            border-bottom: none !important;
        }
    }

    /* 表格内背景颜色 */

    :deep(.el-table) tr,
    :deep(.el-table) td {
        background-color: transparent !important;
    }

    :deep(.el-table) tr td {
        background-color: transparent !important;
        height: 58px;
        color: #fff;
        text-align: center;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
        letter-spacing: -0.28px;
        // border-color: #30ABE8 !important;
    }
    // 表头样式（去掉底部边框）
    :deep(.el-table__header) {
        .el-table__cell {
            border-bottom: none !important;
            background-color: rgba(25, 159, 255, 0.1) !important;
            color: rgba(128, 234, 255, 1);
        }
    }

    // 表格内容样式（保留底部边框）
    :deep(.el-table__body) {
        .el-table__cell {
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;
            height: 40px;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color: rgba(255, 255, 255, 1);
        }
    }

    /* 去掉table 最底部的线 */
    :deep(.el-table__inner-wrapper::before) {
        left: 0;
        bottom: 0;
        width: 100%;
        height: 0;
        z-index: 3;
    }

    .pagination {
        margin-top: 16px;
    }

    :deep(.el-pagination .el-pager li:not(.is-active)) {
        background-color: transparent;
        border: 1px solid rgba(26, 159, 255, 1);
        /*进行修改未选中背景和字体*/
        color: rgba(26, 159, 255, 1);
        text-align: center;
        border-radius: 1px;
        border: 1px solid #30abe8;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
    }

    :deep(.el-pagination .el-pager li:not(.is-disabled).is-active) {
        border-radius: 1px;
        border: 1px solid rgba(255, 198, 26, 1);
        background: rgba(255, 198, 26, 0.15);

        color: #47ebeb;
        text-align: center;

        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: rgba(255, 255, 255, 1);
    }

    :deep(.el-pagination .el-pager li:hover) {
        border-radius: 1px;
        border: 1px solid rgba(255, 198, 26, 1);
        background: rgba(255, 198, 26, 0.15);

        color: #47ebeb;
        text-align: center;

        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: rgba(255, 255, 255, 1);
    }

    :deep(.el-pagination .btn-next) {
        background-color: transparent;
        border: 1px solid rgba(26, 159, 255, 1);
        /*进行修改未选中背景和字体*/
        color: rgba(26, 159, 255, 1);
        text-align: center;
        border-radius: 1px;
        border: 1px solid #30abe8;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
    }

    :deep(.el-pagination .btn-prev) {
        background-color: transparent;
        border: 1px solid rgba(26, 159, 255, 1);
        /*进行修改未选中背景和字体*/
        color: rgba(26, 159, 255, 1);
        text-align: center;
        border-radius: 1px;
        border: 1px solid #30abe8;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
    }

    :deep(.el-pagination__total) {
        background-color: transparent;
        font-family: Noto Sans SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        text-align: right;
        vertical-align: middle;
        color: rgba(26, 159, 255, 1);
    }
}
</style>
