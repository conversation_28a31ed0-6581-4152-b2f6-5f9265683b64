<svg width="603" height="300" viewBox="0 0 603 300" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_289_57)">
<g clip-path="url(#clip0_289_57)">
<rect width="603" height="300" rx="4" fill="#05202E" fill-opacity="0.6"/>
<rect width="758" height="52" transform="translate(-77)" fill="url(#paint0_linear_289_57)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M633 27H113V26H633V27Z" fill="url(#paint1_linear_289_57)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M238.5 52L245.5 47H250.5L243.5 52H238.5ZM248.5 52L255.5 47H260.5L253.5 52H248.5ZM265.5 47L258.5 52H263.5L270.5 47H265.5ZM268.5 52L275.5 47H280.5L273.5 52H268.5Z" fill="url(#paint2_linear_289_57)"/>
<path d="M331.343 27L314.333 43.0001L247.101 43C245.305 43 243.555 43.5691 242.102 44.6257L235.31 49.5655C234.028 50.4978 232.484 51 230.899 51H-77V52H230.899C232.695 52 234.445 51.4309 235.898 50.3743L242.69 45.4345C243.972 44.5022 245.516 44 247.101 44L314.667 44.0001L333 27H331.343Z" fill="url(#paint3_linear_289_57)"/>
<path d="M-77 42.8802V31H248.183L233.942 44.6214C232.454 46.0454 230.473 46.8402 228.413 46.8402H-77V42.8802Z" fill="url(#paint4_linear_289_57)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M247.296 31L233.439 44.2863C232.153 45.517 230.603 46.1799 229.012 46.1799H-77V47.4999H229.012C230.815 47.4999 232.571 46.7486 234.029 45.3539L249 31H247.296Z" fill="url(#paint5_linear_289_57)"/>
</g>
<rect x="0.5" y="0.5" width="602" height="299" rx="3.5" stroke="url(#paint6_linear_289_57)"/>
</g>
<defs>
<filter id="filter0_b_289_57" x="-4" y="-4" width="611" height="308" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_289_57"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_289_57" result="shape"/>
</filter>
<linearGradient id="paint0_linear_289_57" x1="426.375" y1="0" x2="426.375" y2="52" gradientUnits="userSpaceOnUse">
<stop stop-color="#30ABE8" stop-opacity="0"/>
<stop offset="1" stop-color="#30ABE8" stop-opacity="0.6"/>
</linearGradient>
<linearGradient id="paint1_linear_289_57" x1="113" y1="26.5004" x2="633" y2="26.5004" gradientUnits="userSpaceOnUse">
<stop stop-color="#47EBEB" stop-opacity="0"/>
<stop offset="0.703125" stop-color="#47EBEB"/>
<stop offset="1" stop-color="#47EBEB" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_289_57" x1="238.5" y1="52" x2="280.5" y2="52" gradientUnits="userSpaceOnUse">
<stop stop-color="#47EBEB"/>
<stop offset="1" stop-color="#47EBEB" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_289_57" x1="-77" y1="51.4976" x2="333" y2="51.4976" gradientUnits="userSpaceOnUse">
<stop offset="0.122809" stop-color="#47EBEB" stop-opacity="0"/>
<stop offset="0.713948" stop-color="#47EBEB"/>
<stop offset="1" stop-color="#47EBEB" stop-opacity="0.15"/>
</linearGradient>
<linearGradient id="paint4_linear_289_57" x1="192.229" y1="28.36" x2="192.229" y2="46.8402" gradientUnits="userSpaceOnUse">
<stop offset="0.350597" stop-color="#47EBEB" stop-opacity="0"/>
<stop offset="1" stop-color="#47EBEB" stop-opacity="0.6"/>
</linearGradient>
<linearGradient id="paint5_linear_289_57" x1="192.126" y1="30.4663" x2="192.126" y2="47.5003" gradientUnits="userSpaceOnUse">
<stop offset="0.291667" stop-color="#47EBEB" stop-opacity="0"/>
<stop offset="1" stop-color="#47EBEB"/>
</linearGradient>
<linearGradient id="paint6_linear_289_57" x1="301.5" y1="0" x2="301.5" y2="300" gradientUnits="userSpaceOnUse">
<stop stop-color="#47EBEB" stop-opacity="0.15"/>
<stop offset="1" stop-color="#47EBEB" stop-opacity="0.6"/>
</linearGradient>
<clipPath id="clip0_289_57">
<rect width="603" height="300" rx="4" fill="white"/>
</clipPath>
</defs>
</svg>
