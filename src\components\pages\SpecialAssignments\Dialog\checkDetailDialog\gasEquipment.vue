<template>
  <div class="check-detail-wrapper">
   
    <div class="bottom">
                    <el-table
                        class="tablebox"
                        :data="tableData"
                        style="width: 100%"
                    >
                        <el-table-column
                            prop="equipmentType"
                            label="设备类型"
                            show-overflow-tooltip
                            width="100"

                        >
                        <template #default="scope">
                            {{ dictList&&dictList.find(i=>i.dictEncoding==scope.row.equipmentType)!=undefined?dictList.find(i=>i.dictEncoding==scope.row.equipmentType).dictName:'-' }}
                        </template>
                        </el-table-column>
                        <el-table-column
                            prop="equipmentCode"
                            label="设备编号"
                            show-overflow-tooltip
                            width="140"
                        />
                        <el-table-column
                            prop="equipmentName"
                            label="设备名称"
                            show-overflow-tooltip
                            width="140"

                        >
                        <!-- <template #default="scope">
                            {{ dictList&&dictList.find(i=>i.dictEncoding==scope.row.nature)!=undefined?dictList.find(i=>i.dictEncoding==scope.row.nature).dictName:'-' }}
                        </template> -->
                        </el-table-column>
                        <el-table-column
                            prop="monitoringStationName"
                            label="监测站"
                            show-overflow-tooltip
                            width="120"

                        />
                        <el-table-column
                            prop="acquisitionTime"
                            label="采集时间"
                            show-overflow-tooltip
                            width="160"

                        />
                        <el-table-column
                            prop="factorName"
                            label="污染因子"
                            show-overflow-tooltip
                            width="100"
                        />
                        <el-table-column
                            prop="monitorValue"
                            label="采集数值"
                            show-overflow-tooltip
                            width="100"

                        />
                        

                    </el-table>
                    <el-pagination
                        class="pagination"
                        background
                        v-model:currentPage="pageNum"
                        :page-size="pageSize"
                        @current-change="getData"
                        layout="->,total, prev, pager, next"
                        :total="total"
                    />
                </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed, defineEmits, toRefs } from "vue";
import { getAtmospherePageList } from "@/assets/js/api/dialog/specialAssignments";

const emit = defineEmits(["closeDialog"]);
const props = defineProps({
    dataObj: {
        type: [Object],
    },
});
const { dataObj } = toRefs(props);
let gasIds=ref([]); // 存储选中的 id
const dictList = ref([
    {
        dictEncoding:106001,
        dictName:'大气质量'
    },
    {
        dictEncoding:106002,
        dictName:'地表水质量'
    },
    {
        dictEncoding:106003,
        dictName:'危废环境'
    },
    {
        dictEncoding:106004,
        dictName:'噪音设备'
    },
    {
        dictEncoding:106005,
        dictName:'气象设备'
    },
]);
const tableData = ref([
    
]);

const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);
//获取气体数据
const getData = () =>{
    getAtmospherePageList({
        pageNum:pageNum.value,
        pageSize:pageSize.value,
        equipmentCodes:gasIds.value,
        equipmentType:106001
    }).then((res)=>{
        console.log(res);
        if(res.data&&res.data.list&&res.data.list.length>0){
            tableData.value=res.data.list;
            total.value = res.data.total;
        }
    })

}

const handleCurrentChange = (val) => {
    pageNum.value = val;
    getData()
    console.log('changepsge');
    
   
};
onMounted(()=>{
   if(dataObj.value&&dataObj.value.relateGas&&dataObj.value.relateGas.length>0){
        gasIds.value = dataObj.value.relateGas.map(item => item.equipmentId);
        console.log('onmounted');
        getData()
    }
})
</script>

<style lang="less" scoped>
.check-detail-wrapper{
    width: 760px;
    height: 584px;
    
    .check-item-title{
color: rgba(128, 234, 255, 1);

    }
    .check-item-value-big{
        width: 604px;
    }
    .check-item{
        width: 760px;
    font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: rgba(255, 255, 255, 1);
display: flex;
// align-items: center;
    }
    .bottom {
        width: 760px;
        height: 504px;
        margin-top: 8px;
        .tablebox {
            //表格四个边框的颜色
            // border: 1px solid #30abe8 !important;
            border:none !important;
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;

            th.el-table__cell {
                border: none !important;
            }
        }

        :deep(.el-table),
        :deep(.el-table__expanded-cell) {
            background-color: transparent !important;
            color: #ffffff;
        }

        // 设置表头颜色
        :deep(.el-table th) {
            // border-bottom: 1px solid #30abe8 !important;
            height: 40px;
            background-color: rgba(25, 159, 255, 0.1) !important;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color: rgba(128, 234, 255, 1);
        }

        .el-table__header-wrapper,
        .el-table__fixed-header-wrapper {
            //表头的样式
            th {
                border-bottom: none !important;
            }
        }

        /* 表格内背景颜色 */

        :deep(.el-table) tr,
        :deep(.el-table) td {
            background-color: transparent !important;
        }

        :deep(.el-table) tr td {
            background-color: transparent !important;
            height: 58px;
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            /* 157.143% */
            letter-spacing: -0.28px;
            // border-color: #30ABE8 !important;
        }
          // 表头样式（去掉底部边框）
        :deep(.el-table__header) {
            .el-table__cell {
            border-bottom: none !important;
            background-color: rgba(25, 159, 255, 0.1) !important;
            color: rgba(128, 234, 255, 1);
            }
        }

        // 表格内容样式（保留底部边框）
        :deep(.el-table__body) {
            .el-table__cell {
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;
            height: 40px;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color:rgba(255, 255, 255, 1)
            }
        }

        /* 去掉table 最底部的线 */
        :deep(.el-table__inner-wrapper::before) {
            left: 0;
            bottom: 0;
            width: 100%;
            height: 0;
            z-index: 3;
        }

        .pagination {
            margin-top: 16px;
        }

        :deep(.el-pagination .el-pager li:not(.is-active)) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;

        }

        :deep(.el-pagination .el-pager li:not(.is-disabled).is-active) {
            border-radius: 1px;
            border: 1px solid rgba(255, 198, 26, 1);
            background: rgba(255, 198, 26, 0.15);

            color: #47ebeb;
            text-align: center;

            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: rgba(255, 255, 255, 1);

        }

        :deep(.el-pagination .el-pager li:hover) {
            border-radius: 1px;
            border: 1px solid rgba(255, 198, 26, 1);
            background: rgba(255, 198, 26, 0.15);

            color: #47ebeb;
            text-align: center;

            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            color: rgba(255, 255, 255, 1);
        }

        :deep(.el-pagination .btn-next) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }

        :deep(.el-pagination .btn-prev) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }

        :deep(.el-pagination__total) {
            background-color: transparent;
            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
text-align: right;
vertical-align: middle;
color: rgba(26, 159, 255, 1);
        }
    }
}
</style>