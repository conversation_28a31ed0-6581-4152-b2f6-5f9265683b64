<svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="25" cy="25" r="19.5" fill="#05202E" fill-opacity="0.6"/>
<circle cx="25" cy="25" r="19.5" fill="url(#paint0_linear_61_2722)"/>
<circle cx="25" cy="25" r="19.5" stroke="#30ABE8"/>
<circle cx="25" cy="25" r="20" fill="url(#paint1_linear_61_2722)"/>
<g clip-path="url(#clip0_61_2722)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.5 21C32.8807 21 34 19.8807 34 18.5C34 17.1193 32.8807 16 31.5 16C30.1193 16 29 17.1193 29 18.5C29 19.8807 30.1193 21 31.5 21ZM15.8616 32H25L28 26L23.4432 18.7091C23.2428 18.3885 22.7727 18.3978 22.5851 18.7261L15.4275 31.2519C15.237 31.5853 15.4777 32 15.8616 32ZM30.0528 23.8944L26.3618 31.2764C26.1956 31.6088 26.4373 32 26.809 32H34.191C34.5627 32 34.8044 31.6088 34.6382 31.2764L30.9472 23.8944C30.7629 23.5259 30.237 23.5259 30.0528 23.8944ZM18.5 33C18.2238 33 18 33.2239 18 33.5C18 33.7761 18.2238 34 18.5 34H31.5C31.7761 34 32 33.7761 32 33.5C32 33.2239 31.7761 33 31.5 33H18.5Z" fill="white"/>
</g>
<rect x="1" y="1" width="48" height="48" rx="24" stroke="url(#paint2_linear_61_2722)" stroke-width="1.5"/>
<defs>
<linearGradient id="paint0_linear_61_2722" x1="5" y1="5" x2="45" y2="45" gradientUnits="userSpaceOnUse">
<stop stop-color="#30ABE8" stop-opacity="0"/>
<stop offset="1" stop-color="#30ABE8" stop-opacity="0.6"/>
</linearGradient>
<linearGradient id="paint1_linear_61_2722" x1="9.5" y1="9.5" x2="45" y2="45" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.6"/>
<stop offset="0.354612" stop-color="#30ABE8" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_61_2722" x1="25" y1="1" x2="25" y2="49" gradientUnits="userSpaceOnUse">
<stop stop-color="#30ABE8"/>
<stop offset="0.247917" stop-color="#1271A1"/>
<stop offset="0.466667" stop-color="#30ABE8"/>
<stop offset="0.742708" stop-color="#1271A1"/>
<stop offset="1" stop-color="#30ABE8"/>
</linearGradient>
<clipPath id="clip0_61_2722">
<rect width="24" height="24" fill="white" transform="translate(13 13)"/>
</clipPath>
</defs>
</svg>
