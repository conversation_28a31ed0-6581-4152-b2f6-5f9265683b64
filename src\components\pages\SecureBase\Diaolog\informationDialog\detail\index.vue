<template>
    <div class="dialog-all-wrapper">
        <div class="enterprise-information-dialog-wrapper">
            <div class="enterprise-information-dialog border">
                <div class="dialog-header">
                    <div class="header-title">企业信息</div>
                    <img src="@/assets/newImages/Dialog/dialog-close.svg" class="header-close" @click="closeDialog">
                </div>
                <div class="dialog-content">
                    <div class="top-selects">
                        <img src="@/assets/newImages/Dialog/left-arrow.svg" class="tab-arrow" @click="prevTab"/>
                        <div class="slect-tabs" ref="tabsRef">
                            <div class="tab-item" v-for="(item,index) in topTabs" :key="index" @click="clickTab(item)">
                                <div class="tab-item-text" :class="{'active-tab-item':currentTab === item}" >{{ item }}</div>
                                <div class="tab-item-line" v-if="currentTab === item"></div>
                            </div>
                        </div>
                        <img src="@/assets/newImages/Dialog/right-arrow-icon.svg" class="tab-arrow" @click="nextTab"/>
                    </div>
                    <div class="tab-content">
                        <!-- 内容部分 -->
                        <basic :sonId="sonId" v-if="currentTab=='基本信息'"></basic>
                        <gallery :sonId="sonId" v-if="currentTab=='企业图库'" @openPhotoDialog="openPhotoDialog"></gallery>
                        <plant-workshop :sonId="sonId" v-if="currentTab=='厂房车间'"></plant-workshop>  
                        <product-line :sonId="sonId" v-if="currentTab=='生产线'"></product-line>  
                        <product-equipment :sonId="sonId" v-if="currentTab=='生产设备'"></product-equipment>  
                        <special-equipment :sonId="sonId" v-if="currentTab=='特种设备'"></special-equipment>
                        <firefightEquipment :sonId="sonId" v-if="currentTab=='消防设备'"></firefightEquipment>
                        <craft :sonId="sonId" v-if="currentTab=='工艺'"></craft>  
                        <entrepo :sonId="sonId" v-if="currentTab=='仓库'"></entrepo>  
                        <tank-farm :sonId="sonId" v-if="currentTab=='罐区'"></tank-farm>  
                        <storage-tank :sonId="sonId" v-if="currentTab=='储罐'"></storage-tank>  
                        <raw-material :sonId="sonId" v-if="currentTab=='原辅料'"></raw-material>  
                        <product :sonId="sonId" v-if="currentTab=='产品'"></product>
                        <energy-data-report :sonId="sonId" v-if="currentTab=='产能耗'"></energy-data-report>  
                        <power-consumption :sonId="sonId" v-if="currentTab=='用电量'"></power-consumption>
                        <sensitive-target :sonId="sonId" v-if="currentTab=='周边敏感信息'"></sensitive-target>
                        <solid-waste :sonId="sonId" v-if="currentTab=='固废'"></solid-waste>
                        <fluegas :sonId="sonId" v-if="currentTab=='废气'"></fluegas>
                        <waste-water :sonId="sonId" v-if="currentTab=='废水'"></waste-water>
                    </div>
                </div>
            </div>
            <div class="corner-bottom-left"></div>
        </div>
        <photo v-if="photoShow" :sonId="sonId" :pid="pid" @closePhotoDialog="closeDetail"></photo>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed, defineEmits, toRefs } from "vue";
import photo from "./photo.vue"
const emit = defineEmits(["closeDialog"]);
const props = defineProps({
    sonId: {
        type: [String, Number],
    },
});
const { sonId } = toRefs(props);
//相册id
const pid = ref(null);
//相册弹窗
const photoShow = ref(false);
//打开相册弹窗
const openPhotoDialog = (val) =>{
    pid.value = val;
    photoShow.value = true;
}
//关闭相册弹窗
const closeDetail = (val) => {
    photoShow.value = false;
}
//企业基础信息
import basic from "./basic.vue";
//企业图库
import gallery from "./gallery.vue";
//厂房车间
import plantWorkshop from "./plantWorkshop.vue";
//生产线
import productLine from "./productLine.vue";  
//生产设备
import productEquipment from "./productEquipment.vue"; 
//特种设备
import specialEquipment from "./specialEquipment.vue";
//消防设备
import firefightEquipment from "./firefightEquipment.vue"
//工艺
import craft from "./craft.vue";
//仓库
import entrepo from "./entrepo.vue";
//罐区
import tankFarm from "./tankFarm.vue";
//储罐
import storageTank from "./storageTank.vue";
//原辅料
import rawMaterial from "./rawMaterial.vue";
//产品
import product from "./product.vue"
//产能耗
import energyDataReport from "./energyDataReport.vue";
//用电量
import powerConsumption from "./powerConsumption.vue";
//周边敏感信息
import sensitiveTarget from "./sensitiveTarget.vue"
//固废
import solidWaste from "./solidWaste.vue";
//废气
import fluegas from "./fluegas.vue";
//废水
import wasteWater from "./wasteWater.vue";

//
// import patentFile from "./patentFile.vue"
//顶部tab
let topTabs = ref(['基本信息','企业图库','厂房车间','生产线','生产设备','特种设备','消防设备','工艺','仓库','罐区','储罐','原辅料','产品','产能耗','用电量','周边敏感信息','固废','废气','废水']);
let currentTab = ref('基本信息');
const tabsRef = ref(null);

// 获取当前选中tab的索引
const currentIndex = computed(() => {
    return topTabs.value.indexOf(currentTab.value);
});

// 切换到上一个tab
const prevTab = () => {
    const index = currentIndex.value;
    if (index > 0) {
        currentTab.value = topTabs.value[index - 1];
        scrollToTab(index - 1);
    }
};

// 切换到下一个tab
const nextTab = () => {
    
    const index = currentIndex.value;
    if (index < topTabs.value.length - 1) {
        currentTab.value = topTabs.value[index + 1];
        scrollToTab(index + 1);
    }
};

// 滚动到指定tab
const scrollToTab = (index) => {
    if (tabsRef.value) {
        const tabElements = tabsRef.value.getElementsByClassName('tab-item');
        if (tabElements[index]) {
            tabElements[index].scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
        }
    }
};

const clickTab = (item) => {
    currentTab.value = item;
    const index = topTabs.value.indexOf(item);
    scrollToTab(index);
};

onMounted(() => {
    console.log(sonId.value,'详情---------');
    
});

const closeDialog = () => {
    emit('closeDialog','informationDialogShow');
};
</script>

<style lang="less" scoped>
.tab-arrow {
    width: 16px;
    height: 16px;
    cursor: pointer;
    flex-shrink: 0;
    
    &:hover {
        opacity: 0.8;
    }
    
    &:active {
        opacity: 0.6;
    }
}

.dialog-all-wrapper {
    width: 100vw;
    height: 100vh;
    z-index: 1003;
    // background-color:antiquewhite;
    background-color: rgba(0, 0, 0, 0.8);

    position: absolute;
    top: 0px;
    left: 0px;
    .enterprise-information-dialog-wrapper {
        width: 1160px;
        height: 776px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        // 添加底部两个装饰三角形
        &::after,
        .corner-bottom-left {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 6px;
            border-color: rgba(26, 159, 255, 0.6) transparent transparent
                transparent;
            z-index: 1;
        }

        // 左下角装饰三角形
        .corner-bottom-left {
            bottom: -7.5px;
            left: -6px;
            transform: rotate(45deg);
        }

        // 右下角装饰三角形
        &::after {
            bottom: -7.5px;
            right: -7.5px;
            transform: rotate(-45deg);
        }
    }
    .enterprise-information-dialog {
        width: 1160px;
        height: 776px;

        background-color: rgba(5, 29, 46, 0.6);

        clip-path: polygon(
            10px 0,/* 左上角开始 */ 
            calc(100% - 10px) 0, /* 右上角左侧 */ 
            100% 10px,/* 右上角下侧 */
            100% calc(100% - 10px),/* 右下角上侧 */ 
            calc(100% - 10px) 100%, /* 右下角左侧 */ 
            10px 100%,/* 左下角右侧 */ 
            0 calc(100% - 10px),/* 左下角上侧 */ 
            0 10px /* 左上角下侧 */
        );
    }
    .dialog-header {
        width: 1128px;
        height: 56px;
        padding: 0 16px;
        margin-left: -1px;
        background: url("@/assets/newImages/Dialog/dialog-header.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-title{
            font-family: MStiffHei PRC;
font-weight: 400;
font-size: 18px;
line-height: 26px;
letter-spacing: 0%;
background: 
        linear-gradient(180deg, rgba(26, 159, 255, 0.45) 20%, rgba(26, 159, 255, 0) 80%),
        linear-gradient(180deg, #FFFFFF 15.63%, #2AD9FF 87.5%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
        }
        .header-close{
            width: 16px;
height: 16px;

        }
    }
    .border {
        border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
        /* 使用渐变作为边框图像 */
        border-image-source: linear-gradient(
            360deg,
            rgba(26, 159, 255, 0.6) 0%,
            rgba(26, 159, 255, 0) 100%
        );
        border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
        border-image-width: 1px;
        border-top: none;
    }
    // 弹窗内部
    .dialog-content{
        width: 1120px;
        height: 680px;
        padding: 20px;
        .top-selects{
            width: 1120px;
            height: 48px;
            border-bottom: 1px solid rgba(128, 234, 255, 0.3);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .slect-tabs {
            width: 1064px;
            height: 48px;
            display: flex;
            align-items: center;
            gap: 20px;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            
            /* 隐藏滚动条 - webkit浏览器 */
            &::-webkit-scrollbar {
                display: none;
            }
            
            /* 隐藏滚动条 - Firefox */
            scrollbar-width: none;
            
            /* 隐藏滚动条 - IE */
            -ms-overflow-style: none;
            
            .tab-item {
                height: 36px;
                padding-top: 12px;
                font-family: Noto Sans SC;
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;
                text-align: center;
                color: rgba(128, 234, 255, 1);
                white-space: nowrap; /* 防止文字换行 */
                flex-shrink: 0; /* 防止项目被压缩 */
            }
            .tab-item-line {
                width: 24px;
                height: 2px;
                background-color: rgba(255, 198, 26, 1);
                margin: auto;
                margin-top: 10px;
            }
            .active-tab-item {
                color: rgba(255, 198, 26, 1);
            }
        }
        .tab-content {
            padding: 16px 0;
        }

    }
    
}
</style>
