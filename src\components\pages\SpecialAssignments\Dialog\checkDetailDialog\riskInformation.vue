<template>
  <div class="check-detail-wrapper">
    <div class="check-item">
        <div class="check-item-title">
            风险辨识结果：
        </div>
        <div class="check-item-value-big" v-if="dataObj&&dataObj.riskResults&&dataObj.riskResults.length>0">
            <el-checkbox-group 
                v-model="taskTypes" 
                class="custom-checkbox-group"
            >
                <el-checkbox
                :disabled="true"
                    v-for="item in dataObj.riskResults"
                    :key="item.id"
                    :label="item.id"
                    :class="{'yellow-checkbox': taskTypes.includes(item.id)}"
                >
                    {{ item.name }}
                </el-checkbox>
            </el-checkbox-group>
        </div>
    </div>
    <div class="check-item">
        <div class="check-item-title">
            安全措施落实：
        </div>
        
    </div>
    <div class="bottom">
                    <el-table
                        class="tablebox"
                        :data="tableData"
                        style="width: 100%"
                    >
                        <el-table-column
                            prop="safetyMeasure"
                            label="安全措施"
                            show-overflow-tooltip
                            width="500"
                        />
                        <el-table-column
                            prop="isInvolve"
                            label="是否涉及"
                            show-overflow-tooltip
                        >
                        <template #default="scope">
                            {{ scope.row.isInvolve?'是':'否' }}
                        </template>
                        </el-table-column>
                        <el-table-column
                            prop="head"
                            label="确认人"
                            show-overflow-tooltip
                        >
                        <!-- <template #default="scope">
                            {{ dictList&&dictList.find(i=>i.dictEncoding==scope.row.nature)!=undefined?dictList.find(i=>i.dictEncoding==scope.row.nature).dictName:'-' }}
                        </template> -->
                        </el-table-column>
                        <el-table-column
                            prop="remark"
                            label="备注"
                            show-overflow-tooltip
                        />
                        

                    </el-table>
                    <el-pagination
                        class="pagination"
                        background
                        v-model:currentPage="pageNum"
                        :page-size="pageSize"
                        @current-change="handleCurrentChange"
                        layout="->,total, prev, pager, next"
                        :total="total"
                    />
                </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed, defineEmits, toRefs } from "vue";
import { check_detail } from "@/assets/js/api/dialog/specialAssignments";

const emit = defineEmits(["closeDialog"]);
const props = defineProps({
    dataObj: {
        type: [Object],
    },
});
const { dataObj } = toRefs(props);
let taskTypes=ref([]); // 存储选中的 id
const tableData = ref([
    
]);
const pageNum = ref(1);
const pageSize = ref(10);
const total = ref(0);


const handleCurrentChange = (val) => {
    pageNum.value = val;
    if(dataObj.value&&dataObj.value.safetyMeasure&&dataObj.value.safetyMeasure.length>0){
        tableData.value = dataObj.value.safetyMeasure.slice((pageNum.value - 1) * pageSize.value, pageNum.value * pageSize.value);
        total.value=dataObj.value.safetyMeasure.length
    }
};
onMounted(()=>{
    console.log(dataObj.value,'详情---------',dataObj.value&&dataObj.value.riskResults&&dataObj.value.riskResults.length>0);
    if(dataObj.value&&dataObj.value.riskResults&&dataObj.value.riskResults.length>0){
        taskTypes.value = dataObj.value.riskResults
        .filter(item => item.isCheck)
        .map(item => item.id);
        console.log(taskTypes.value,'taskTypes');
        

    }
    handleCurrentChange(1);
})
</script>

<style lang="less" scoped>
.check-detail-wrapper{
    width: 760px;
    height: 584px;
    
    .check-item-title{
color: rgba(128, 234, 255, 1);

    }
    .check-item-value-big{
        width: 604px;
    }
    .check-item{
        width: 760px;
    font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: rgba(255, 255, 255, 1);
display: flex;
// align-items: center;
    }
    .bottom {
        width: 760px;
        height: 504px;
        margin-top: 8px;
        .tablebox {
            //表格四个边框的颜色
            // border: 1px solid #30abe8 !important;
            border:none !important;
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;

            th.el-table__cell {
                border: none !important;
            }
        }

        :deep(.el-table),
        :deep(.el-table__expanded-cell) {
            background-color: transparent !important;
            color: #ffffff;
        }

        // 设置表头颜色
        :deep(.el-table th) {
            // border-bottom: 1px solid #30abe8 !important;
            height: 40px;
            background-color: rgba(25, 159, 255, 0.1) !important;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color: rgba(128, 234, 255, 1);
        }

        .el-table__header-wrapper,
        .el-table__fixed-header-wrapper {
            //表头的样式
            th {
                border-bottom: none !important;
            }
        }

        /* 表格内背景颜色 */

        :deep(.el-table) tr,
        :deep(.el-table) td {
            background-color: transparent !important;
        }

        :deep(.el-table) tr td {
            background-color: transparent !important;
            height: 58px;
            color: #fff;
            text-align: center;
            font-family: Noto Sans SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            /* 157.143% */
            letter-spacing: -0.28px;
            // border-color: #30ABE8 !important;
        }
          // 表头样式（去掉底部边框）
        :deep(.el-table__header) {
            .el-table__cell {
            border-bottom: none !important;
            background-color: rgba(25, 159, 255, 0.1) !important;
            color: rgba(128, 234, 255, 1);
            }
        }

        // 表格内容样式（保留底部边框）
        :deep(.el-table__body) {
            .el-table__cell {
            border-bottom: 1px solid rgba(25, 159, 255, 0.3) !important;
            height: 40px;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            color:rgba(255, 255, 255, 1)
            }
        }

        /* 去掉table 最底部的线 */
        :deep(.el-table__inner-wrapper::before) {
            left: 0;
            bottom: 0;
            width: 100%;
            height: 0;
            z-index: 3;
        }

        .pagination {
            margin-top: 16px;
        }

        :deep(.el-pagination .el-pager li:not(.is-active)) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;

        }

        :deep(.el-pagination .el-pager li:not(.is-disabled).is-active) {
            border-radius: 1px;
            border: 1px solid rgba(255, 198, 26, 1);
            background: rgba(255, 198, 26, 0.15);

            color: #47ebeb;
            text-align: center;

            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: rgba(255, 255, 255, 1);

        }

        :deep(.el-pagination .el-pager li:hover) {
            border-radius: 1px;
            border: 1px solid rgba(255, 198, 26, 1);
            background: rgba(255, 198, 26, 0.15);

            color: #47ebeb;
            text-align: center;

            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            color: rgba(255, 255, 255, 1);
        }

        :deep(.el-pagination .btn-next) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }

        :deep(.el-pagination .btn-prev) {
            background-color: transparent;
            border: 1px solid rgba(26, 159, 255, 1);
            /*进行修改未选中背景和字体*/
            color: rgba(26, 159, 255, 1);
            text-align: center;
            border-radius: 1px;
            border: 1px solid #30abe8;
            font-family: Noto Sans SC;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }

        :deep(.el-pagination__total) {
            background-color: transparent;
            font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
text-align: right;
vertical-align: middle;
color: rgba(26, 159, 255, 1);
        }
    }
}
</style>